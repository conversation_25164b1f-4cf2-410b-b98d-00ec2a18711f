# 邮件告警推送程序

一个基于PySide6的GUI应用程序，用于监控指定邮箱的新邮件，识别包含特定关键字的告警邮件，并自动推送到钉钉群聊。

## 功能特性

### 📧 邮箱监控
- 支持多个邮箱账户配置
- 支持IMAP协议（Gmail、Outlook、QQ邮箱等）
- 自动识别新邮件
- 可配置自动刷新间隔
- 支持手动立即刷新

### 🔍 智能匹配
- 可配置关键字列表
- 支持邮件主题和内容匹配
- 默认关键字："告警通知"
- 支持多个关键字，任意匹配即可触发

### 📤 钉钉推送
- 支持多个钉钉机器人Webhook
- 自动推送匹配的告警邮件
- 支持手动推送选中邮件
- 推送内容包含邮件主题、发件人、时间和内容摘要

### 🖥️ 友好界面
- 现代化的GUI界面
- 邮件列表实时显示
- 邮件详情查看
- 新邮件明显标识
- 推送状态跟踪

## 安装要求

### 系统要求
- Windows 10/11
- Python 3.8+

### 依赖包
```bash
pip install -r requirements.txt
```

主要依赖：
- PySide6 >= 6.5.0 (GUI框架)
- requests >= 2.28.0 (HTTP请求)
- cryptography >= 3.4.8 (密码加密)

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动程序
```bash
python run.py
```
或者
```bash
python main.py
```

### 3. 配置邮箱
1. 点击工具栏的"⚙️ 设置"按钮
2. 在"📧 邮箱配置"选项卡中添加邮箱账户
3. 填写邮箱地址、密码/授权码、IMAP服务器等信息
4. 点击"🔧 测试连接"验证配置
5. 保存配置

### 4. 配置Webhook
1. 在设置对话框中切换到"🔗 Webhook配置"选项卡
2. 添加钉钉机器人的Webhook地址
3. 点击"🔧 测试"验证Webhook是否可用
4. 保存配置

### 5. 配置关键字
1. 在设置对话框中切换到"🔍 关键字配置"选项卡
2. 添加需要匹配的关键字（默认为"告警通知"）
3. 保存配置

## 使用说明

### 邮箱配置
支持的邮箱服务商及其配置：

| 邮箱服务商 | IMAP服务器 | 端口 | 加密方式 |
|-----------|-----------|------|---------|
| Gmail | imap.gmail.com | 993 | SSL/TLS |
| Outlook/Hotmail | outlook.office365.com | 993 | SSL/TLS |
| QQ邮箱 | imap.qq.com | 993 | SSL/TLS |
| 163邮箱 | imap.163.com | 993 | SSL/TLS |
| 126邮箱 | imap.126.com | 993 | SSL/TLS |

**注意：** 大部分邮箱需要开启IMAP服务并使用授权码而不是登录密码。

### 钉钉机器人配置
1. 在钉钉群中添加自定义机器人
2. 获取Webhook地址，格式如：
   ```
   https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key
   ```
3. 将Webhook地址添加到程序配置中

### 自动刷新设置
- 默认每60秒自动检查一次新邮件
- 可在"⚙️ 通用设置"中调整刷新间隔
- 可以关闭自动刷新，仅使用手动刷新

## 目录结构

```
邮件告警推送程序/
├── main.py                 # 主程序入口
├── run.py                  # 启动脚本
├── requirements.txt        # 依赖包列表
├── README.md              # 说明文档
├── test_basic.py          # 基本功能测试
├── test_gui.py            # GUI测试
├── src/                   # 源代码目录
│   ├── config/            # 配置管理
│   ├── core/              # 核心服务
│   ├── email_handler/     # 邮件处理
│   ├── pusher/            # 推送功能
│   ├── gui/               # GUI界面
│   └── utils/             # 工具模块
├── config/                # 配置文件目录（自动创建）
└── logs/                  # 日志文件目录（自动创建）
```

## 故障排除

### 常见问题

1. **邮箱连接失败**
   - 检查邮箱是否开启了IMAP服务
   - 确认使用的是授权码而不是登录密码
   - 检查网络连接和防火墙设置

2. **钉钉推送失败**
   - 验证Webhook地址是否正确
   - 检查钉钉机器人是否被禁用
   - 确认网络可以访问钉钉API

3. **程序启动失败**
   - 检查Python版本是否为3.8+
   - 确认所有依赖包已正确安装
   - 查看日志文件获取详细错误信息

### 日志文件
程序运行时会在`logs/`目录下生成日志文件，包含详细的运行信息和错误信息。

## 开发说明

### 测试
```bash
# 基本功能测试
python test_basic.py

# GUI测试
python test_gui.py
```

### 项目架构
- **配置管理**: 使用JSON文件存储配置，密码加密存储
- **邮件处理**: 基于IMAP协议的邮件客户端
- **推送服务**: 钉钉机器人API集成
- **GUI界面**: PySide6现代化界面设计
- **日志系统**: 分级日志记录和文件轮转

## 许可证

本项目采用MIT许可证。

## 更新日志

### v1.0.20 (2025-08-16) - 🎯 邮件推送逻辑修复
- 🕐 **新邮件检测逻辑修复**: 基于真实时间而非仅缓存状态判断新邮件
- ⏰ **时间容错机制**: 添加5分钟容错处理时区和服务器时间差问题
- 🗂️ **缓存清理安全**: 修复缓存清理后可能误判旧邮件为新邮件的问题
- 🔍 **严格时间验证**: 只有邮件时间晚于检查时间才标记为新邮件
- 📝 **详细时间日志**: 添加时间比较的详细调试日志
- ✅ 添加邮件推送逻辑修复测试脚本，所有测试通过

### v1.0.19 (2025-08-16) - 🔴 高优先级问题修复
- 🛡️ **内存泄漏修复**: 添加邮件缓存限制(1000封)和定期清理机制(30天)
- 🔗 **连接泄漏修复**: IMAP连接超时设置、重试机制(指数退避)、析构函数确保连接关闭
- ⚡ **异常处理增强**: 完整的异常捕获处理、优雅错误恢复、详细错误日志记录
- 📊 **内存监控**: 添加内存使用信息获取和实时监控功能
- 🔄 **连接管理**: 连接状态检测、自动重连、空闲连接清理
- ✅ 添加高优先级问题修复测试脚本，所有测试通过

### v1.0.18 (2025-08-16)
- 🔤 实现紧凑格式显示功能，让字体更小、信息更密集
- 🌐 专门优化网络告警邮件的完整信息显示
- 📊 使用markdown_v2格式完整显示所有表格数据和详细信息
- 🎯 智能识别网络设备告警，自动应用紧凑格式
- 📝 关键信息使用代码格式（`文本`）显示更紧凑
- ✅ 添加紧凑格式测试脚本和真实推送验证

### v1.0.17 (2025-08-16)
- 📝 实现完整的Markdown语法识别功能
- 🔍 支持17种Markdown语法检测（标题、粗体、斜体、列表、表格、链接等）
- 🎯 Markdown告警邮件智能转换为模板卡片格式
- 🔄 添加Markdown转HTML功能，支持从Markdown内容提取告警信息
- 🧠 智能告警检测，避免误判（排除日报、报告等关键字）
- ✅ 添加Markdown识别测试脚本和真实推送验证

### v1.0.16 (2025-08-16)
- 🎨 实现智能告警邮件模板卡片功能
- 🔍 自动识别告警邮件并转换为企业微信模板卡片格式
- 📊 从HTML内容提取告警级别、时间、描述、详细数据、处理建议
- 🎯 告警邮件使用卡片格式，普通邮件使用markdown_v2格式
- 🌈 支持告警级别颜色标识（严重=红色，中等=绿色，低级=灰色）
- ✅ 添加模板卡片功能测试脚本和真实推送测试

### v1.0.15 (2025-08-15)
- 🎨 实现HTML邮件内容转换为markdown_v2格式功能
- 🔍 添加智能内容格式检测（HTML/Markdown/Text）
- 📝 支持HTML标题、粗体、斜体、链接、列表、表格等元素转换
- 🚀 告警邮件自动转换为美观的结构化格式
- ✅ 添加HTML转markdown_v2功能测试脚本

### v1.0.14 (2025-08-15)
- 🔄 修复手动刷新不重置倒计时的问题
- ⏰ 手动刷新后自动重置倒计时，与自动刷新逻辑统一
- 🎯 添加统一的倒计时重置方法，提高代码复用性
- 💡 改进用户体验，手动刷新=立即刷新+重置倒计时
- ✅ 添加手动刷新重置倒计时测试脚本

### v1.0.13 (2025-08-15)
- 📊 添加详细的邮件统计日志，显示新邮件和旧邮件数量
- 🔍 改进DEBUG级别日志，提供完整的邮件分布信息
- 📝 优化INFO级别日志，使用更友好的格式显示状态
- 🏷️ 区分首次启动和正常刷新的日志标识
- ✅ 添加详细日志格式测试脚本

### v1.0.12 (2025-08-15)
- 🎯 彻底简化新邮件检测逻辑，基于缓存而非复杂时间比较
- ⚡ 移除复杂的时间容错机制，使用"不在缓存中=新邮件"的简单逻辑
- 🚫 彻底解决"时间过旧"问题，不再依赖邮件时间判断
- 📦 优化邮件缓存机制，提高检测准确性和性能
- ✅ 添加简化逻辑测试脚本，验证新的检测机制

### v1.0.10 (2025-08-15)
- 🔥 根本修复新邮件推送问题，解决"有新邮件但不推送"的问题
- 🔧 修复首次运行时错误标记历史邮件为新邮件的逻辑
- ⏰ 正确设置邮件检查时间基准，确保后续新邮件能被检测
- 🔍 启用DEBUG日志级别，提供详细的调试信息
- ✅ 添加新邮件检测修复测试脚本

### v1.0.9 (2025-08-15)
- ⏰ 添加自动刷新倒计时显示，实时显示剩余秒数
- 🐛 修复新邮件检测和推送逻辑问题
- 🔍 添加详细的调试日志，便于问题排查
- 🔧 改进邮件状态合并机制，确保新邮件正确处理
- ✅ 添加倒计时和新邮件推送测试脚本

### v1.0.8 (2025-08-15)
- ✨ 简化钉钉推送消息格式，只推送邮件内容
- 🗑️ 移除推送消息中的标题、发件人、时间信息
- 📝 推送消息更加简洁直接，专注核心内容
- 🔧 增加推送内容长度限制到2000字符
- ✅ 添加简化推送格式测试脚本

### v1.0.7 (2025-08-15)
- 🐛 修复主界面无法显示邮箱连接状态的问题
- 🔐 修复密码保存问题，不再需要每次重新输入
- ✨ 添加连接状态实时监控和显示
- 🎨 使用占位符显示已保存密码状态
- 🔧 修复配置管理器重复加密密码的问题

### v1.0.6 (2025-08-15)
- ✨ 新增主界面状态显示功能
- 🎨 工具栏显示当前邮箱和自动刷新状态
- 📊 状态栏显示连接状态、Webhook状态和邮件统计
- 🎯 不同状态使用不同颜色标识，一目了然
- 🔄 状态信息根据配置和邮件实时更新

### v1.0.5 (2025-08-15)
- 🐛 修复同一封邮件重复推送的问题
- 🔧 优化邮件状态管理，处理后立即标记为已处理
- 📝 同步更新邮件缓存状态，确保状态一致性
- ✨ 改进首次刷新逻辑，避免后续重复推送
- ✅ 添加重复推送修复测试脚本

### v1.0.4 (2025-08-15)
- 🐛 修复时区比较错误"can't compare offset-naive and offset-aware datetimes"
- 🔧 统一使用UTC时区，自动处理邮件日期时区信息
- 📝 优化配置对话框加载逻辑，避免重复加载
- ✨ 添加时区处理调试日志
- ✅ 添加时区修复测试脚本

### v1.0.3 (2025-08-15)
- 🐛 修复钉钉推送"empty content"错误
- 🔧 改用text格式消息，提高推送成功率
- 📝 添加推送消息调试日志
- ✨ 优化空内容邮件的处理
- ✅ 添加推送功能测试脚本

### v1.0.2 (2025-08-15)
- 🐛 修复邮件刷新后状态丢失的问题
- ✨ 实现邮件缓存机制，避免重复获取邮件详情
- 🔧 添加邮件状态合并功能，保持已读、已推送等状态
- 📝 完善邮件状态持久化测试

### v1.0.1 (2025-08-15)
- 🐛 修复冷启动时误将所有邮件当作新邮件推送的问题
- 🐛 移除推送失败时的弹窗提示，改为状态栏显示
- 🔧 改进新邮件识别逻辑，基于时间戳判断真正的新邮件
- 📝 完善错误处理和日志记录
- ✅ 添加修复效果测试脚本

### v1.0.0 (2025-08-15)
- 初始版本发布
- 支持多邮箱监控
- 钉钉推送功能
- 关键字匹配
- GUI界面
- 配置管理
- 日志系统
