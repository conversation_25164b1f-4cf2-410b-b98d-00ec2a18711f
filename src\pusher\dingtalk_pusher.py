#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简洁推送器 - Linus式设计
遵循"做一件事并做好"的原则：只负责推送消息，不做复杂的格式检测和转换
"""

import requests
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
from src.utils.logger import get_logger
from src.email_handler.attachment_handler import ImageAttachment

logger = get_logger(__name__)


class SimplePusher:
    """简洁推送器 - Linus式设计
    
    核心原则：
    1. 单一职责：只负责推送消息
    2. 简单可靠：统一使用markdown格式
    3. 消除特殊情况：不做复杂的格式检测
    """
    
    def __init__(self, webhook_urls: List[str]):
        """初始化推送器
        
        Args:
            webhook_urls: Webhook URL列表
        """
        self.webhook_urls = webhook_urls
        self.timeout = 10
        
        logger.info(f"📤 简洁推送器初始化，Webhook数量: {len(webhook_urls)}")
    
    def push_message(self, title: str, content: str, sender: str = "", 
                    date: datetime = None) -> Tu<PERSON>[bool, str]:
        """推送消息 - 核心功能
        
        Args:
            title: 消息标题
            content: 消息内容
            sender: 发送者
            date: 发送时间
            
        Returns:
            (是否成功, 错误信息)
        """
        if not self.webhook_urls:
            return False, "未配置Webhook URL"
        
        # 构建简洁的消息
        message = self._build_simple_message(title, content, sender, date)
        
        # 推送到所有Webhook
        success_count = 0
        error_messages = []
        
        for webhook_url in self.webhook_urls:
            try:
                success, error = self._send_to_webhook(webhook_url, message)
                if success:
                    success_count += 1
                else:
                    error_messages.append(error)
            except Exception as e:
                error_messages.append(f"推送异常: {str(e)}")
        
        # 判断整体结果
        if success_count > 0:
            logger.info(f"✅ 消息推送成功: {success_count}/{len(self.webhook_urls)}个Webhook")
            return True, f"成功推送到{success_count}个Webhook"
        else:
            error_msg = "; ".join(error_messages)
            logger.error(f"❌ 消息推送失败: {error_msg}")
            return False, error_msg
    
    def _build_simple_message(self, title: str, content: str, sender: str = "",
                             date: datetime = None) -> Dict[str, Any]:
        """构建简洁消息 - 企业微信兼容格式

        使用text格式，确保企业微信能正确接收
        """
        # 格式化时间
        time_str = date.strftime("%Y-%m-%d %H:%M:%S") if date else datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 构建简洁的文本消息
        text_content = self._format_as_text(title, content, sender, time_str)

        return {
            "msgtype": "text",
            "text": {
                "content": text_content
            }
        }
    
    def _format_as_text(self, title: str, content: str, sender: str, time_str: str) -> str:
        """格式化为纯正文内容 - 只保留邮件正文

        根据用户需求，只推送邮件正文内容，不包含标题、发件人、时间等元数据
        """
        # 只处理邮件内容
        if content and content.strip():
            # 清理内容并去重
            clean_content = self._clean_content(content)

            # 限制长度
            if len(clean_content) > 1500:
                clean_content = clean_content[:1500] + "...(内容过长，已截断)"

            if clean_content.strip():
                return clean_content

        # 如果没有内容，返回简单提示
        return "邮件内容为空"
    
    def _clean_content(self, content: str) -> str:
        """清理内容并去除重复 - Linus式完整清理

        处理：HTML标签、HTML实体、重复内容、多余空白
        """
        import re
        import html

        # Step 1: 解码HTML实体 (&nbsp; &gt; &lt; &amp; 等)
        content = html.unescape(content)

        # Step 2: 按行分割内容
        lines = content.split('\n')

        # Step 3: 处理每一行
        cleaned_lines = []
        seen_text = set()  # 用于去重

        for line in lines:
            # 移除HTML标签
            clean_line = re.sub(r'<[^>]+>', '', line)

            # 移除HTML注释
            clean_line = re.sub(r'<!--.*?-->', '', clean_line)

            # 清理多余空白（保留单个空格）
            clean_line = re.sub(r'[ \t]+', ' ', clean_line).strip()

            # 移除只包含特殊字符的行
            if re.match(r'^[=\-_*#]+$', clean_line):
                continue

            # 如果行不为空且未见过（去重）
            if clean_line and clean_line not in seen_text:
                cleaned_lines.append(clean_line)
                seen_text.add(clean_line)

        # Step 4: 合并结果
        result = '\n'.join(cleaned_lines)

        # Step 5: 最终清理
        # 移除多余的空行（超过2个连续换行）
        result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)

        # 移除开头和结尾的空白
        result = result.strip()

        return result

    def push_image_attachments(self, attachments: List[ImageAttachment],
                             push_as_file: bool = False) -> Tuple[bool, str]:
        """推送图片附件

        Args:
            attachments: 图片附件列表
            push_as_file: 是否作为文件推送（False=图片消息，True=文件消息）

        Returns:
            (是否成功, 错误信息)
        """
        if not attachments:
            return True, "无图片附件"

        if not self.webhook_urls:
            return False, "未配置Webhook URL"

        success_count = 0
        error_messages = []

        for attachment in attachments:
            try:
                if push_as_file:
                    # 作为文件推送（支持更大文件）
                    success, error = self._push_image_as_file(attachment)
                else:
                    # 作为图片推送（直接显示）
                    success, error = self._push_image_as_image(attachment)

                if success:
                    success_count += 1
                    logger.info(f"📷 图片推送成功: {attachment.filename}")
                else:
                    error_messages.append(f"{attachment.filename}: {error}")
                    logger.error(f"📷 图片推送失败: {attachment.filename}, 错误: {error}")

            except Exception as e:
                error_messages.append(f"{attachment.filename}: 推送异常 {str(e)}")
                logger.error(f"📷 图片推送异常: {attachment.filename}, 错误: {e}")

        # 返回结果
        if success_count == len(attachments):
            return True, f"成功推送 {success_count} 张图片"
        elif success_count > 0:
            return True, f"部分成功：推送 {success_count}/{len(attachments)} 张图片"
        else:
            error_msg = "; ".join(error_messages)
            return False, f"图片推送失败: {error_msg}"

    def _push_image_as_image(self, attachment: ImageAttachment) -> Tuple[bool, str]:
        """作为图片消息推送"""
        try:
            # 构建图片消息
            message = {
                "msgtype": "image",
                "image": {
                    "base64": attachment.get_base64(),
                    "md5": attachment.get_md5()
                }
            }

            # 推送到所有Webhook
            success_count = 0
            error_messages = []

            for webhook_url in self.webhook_urls:
                success, error = self._send_to_webhook(webhook_url, message)
                if success:
                    success_count += 1
                else:
                    error_messages.append(error)

            if success_count > 0:
                return True, "推送成功"
            else:
                return False, "; ".join(error_messages)

        except Exception as e:
            return False, f"构建图片消息失败: {str(e)}"

    def _push_image_as_file(self, attachment: ImageAttachment) -> Tuple[bool, str]:
        """作为文件消息推送（需要先上传文件获取media_id）"""
        try:
            # 上传文件获取media_id
            media_id = self._upload_file(attachment)
            if not media_id:
                return False, "文件上传失败"

            # 构建文件消息
            message = {
                "msgtype": "file",
                "file": {
                    "media_id": media_id
                }
            }

            # 推送到所有Webhook
            success_count = 0
            error_messages = []

            for webhook_url in self.webhook_urls:
                success, error = self._send_to_webhook(webhook_url, message)
                if success:
                    success_count += 1
                else:
                    error_messages.append(error)

            if success_count > 0:
                return True, "推送成功"
            else:
                return False, "; ".join(error_messages)

        except Exception as e:
            return False, f"文件推送失败: {str(e)}"

    def _upload_file(self, attachment: ImageAttachment) -> str:
        """上传文件到企业微信，返回media_id"""
        try:
            # 从webhook URL中提取key
            if not self.webhook_urls:
                return ""

            webhook_url = self.webhook_urls[0]
            if "key=" not in webhook_url:
                return ""

            key = webhook_url.split("key=")[1].split("&")[0]

            # 构建上传URL
            upload_url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key={key}&type=file"

            # 准备文件数据
            files = {
                'media': (
                    attachment.filename,
                    attachment.content,
                    attachment.content_type
                )
            }

            # 上传文件
            response = requests.post(upload_url, files=files, timeout=self.timeout)

            if response.status_code == 200:
                result = response.json()
                if result.get("errcode") == 0:
                    media_id = result.get("media_id")
                    logger.info(f"📁 文件上传成功: {attachment.filename}, media_id: {media_id}")
                    return media_id
                else:
                    logger.error(f"📁 文件上传失败: {result.get('errmsg', '未知错误')}")
                    return ""
            else:
                logger.error(f"📁 文件上传HTTP错误: {response.status_code}")
                return ""

        except Exception as e:
            logger.error(f"📁 文件上传异常: {e}")
            return ""
    
    def _send_to_webhook(self, webhook_url: str, message: Dict[str, Any]) -> Tuple[bool, str]:
        """发送到单个Webhook"""
        try:
            response = requests.post(
                webhook_url,
                json=message,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout,
                verify=True
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("errcode") == 0:
                    return True, "推送成功"
                else:
                    return False, f"推送失败: {result.get('errmsg', '未知错误')}"
            else:
                return False, f"HTTP错误: {response.status_code}"
                
        except requests.exceptions.Timeout:
            return False, "请求超时"
        except requests.exceptions.RequestException as e:
            return False, f"网络错误: {str(e)}"
        except Exception as e:
            return False, f"未知错误: {str(e)}"
    
    def get_webhooks(self) -> List[str]:
        """获取Webhook URL列表"""
        return self.webhook_urls.copy()
    
    def add_webhook(self, webhook_url: str):
        """添加Webhook URL"""
        if webhook_url not in self.webhook_urls:
            self.webhook_urls.append(webhook_url)
            logger.info(f"添加Webhook: {webhook_url}")
    
    def remove_webhook(self, webhook_url: str):
        """移除Webhook URL"""
        if webhook_url in self.webhook_urls:
            self.webhook_urls.remove(webhook_url)
            logger.info(f"移除Webhook: {webhook_url}")

    def test_webhook(self, webhook_url: str) -> tuple[bool, str]:
        """测试Webhook连接

        Args:
            webhook_url: Webhook URL

        Returns:
            tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 构建测试消息
            test_message = {
                "msgtype": "text",
                "text": {
                    "content": f"📧 邮件告警系统连接测试\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n状态: 连接正常"
                }
            }

            # 发送测试消息
            response = requests.post(
                webhook_url,
                json=test_message,
                headers={"Content-Type": "application/json"},
                timeout=10,
                verify=True
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("errcode") == 0:
                    return True, "连接测试成功"
                else:
                    return False, f"Webhook返回错误: {result.get('errmsg', '未知错误')}"
            else:
                return False, f"HTTP错误: {response.status_code}"

        except requests.exceptions.Timeout:
            return False, "连接超时"
        except requests.exceptions.RequestException as e:
            return False, f"网络错误: {str(e)}"
        except Exception as e:
            return False, f"测试失败: {str(e)}"


# 为了兼容性，创建一个别名
DingTalkPusher = SimplePusher
