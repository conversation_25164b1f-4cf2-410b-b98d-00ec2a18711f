@echo off
chcp 65001 >nul
echo 邮件告警推送程序打包脚本
echo.

echo 检查PyInstaller...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo 安装PyInstaller失败，请手动安装
        pause
        exit /b 1
    )
)

echo 清理旧文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo 开始打包...
pyinstaller --onefile --windowed --name=邮件告警推送程序 --add-data=config;config --hidden-import=PySide6.QtCore --hidden-import=PySide6.QtWidgets --hidden-import=PySide6.QtGui --clean main.py

if errorlevel 1 (
    echo 打包失败！
    pause
    exit /b 1
)

echo 复制配置文件...
if exist config xcopy config dist\config\ /E /I /Y

echo.
echo 打包完成！
echo 输出文件: dist\邮件告警推送程序.exe
echo.
pause
