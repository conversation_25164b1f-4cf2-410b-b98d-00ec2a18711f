2025-08-18 00:31:14 - src.gui.main_window - INFO - 邮件客户端创建成功: <EMAIL>
2025-08-18 00:31:14 - src.pusher.dingtalk_pusher - INFO - 📤 简洁推送器初始化，Webhook数量: 1
2025-08-18 00:31:14 - src.gui.main_window - INFO - 推送器创建成功，Webhook数量: 1
2025-08-18 00:31:14 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 47个
2025-08-18 00:31:14 - src.core.universal_email_pusher - INFO - 📧 邮件告警推送器启动
2025-08-18 00:31:14 - src.core.universal_email_pusher - INFO - 🎯 关键字: ['HZ', 'hz', '告警汇总']
2025-08-18 00:31:14 - src.core.universal_email_pusher - INFO - 📬 邮件客户端: 1个
2025-08-18 00:31:14 - src.core.universal_email_pusher - INFO - 💾 已加载 47 个已见邮件UID
2025-08-18 00:31:14 - src.gui.main_window - INFO - ✅ 邮件告警服务初始化完成
2025-08-18 00:31:15 - src.core.universal_email_pusher - INFO - 🔧 初始化邮件推送器...
2025-08-18 00:31:15 - src.core.universal_email_pusher - DEBUG - 邮件客户端未连接，尝试重连: <EMAIL>
2025-08-18 00:31:15 - src.email_handler.simple_email_client - INFO - 尝试连接到邮箱: <EMAIL> (尝试 1/3)
2025-08-18 00:31:16 - src.email_handler.simple_email_client - INFO - 成功连接到邮箱: <EMAIL>
2025-08-18 00:31:16 - src.email_handler.simple_email_client - DEBUG - 找到 47 封邮件
2025-08-18 00:31:22 - src.email_handler.simple_email_client - INFO - 成功获取 47 封邮件
2025-08-18 00:31:22 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 47封
2025-08-18 00:31:22 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 47 封邮件
2025-08-18 00:31:22 - src.core.universal_email_pusher - INFO - 📧 发现邮箱中有 47 封邮件
2025-08-18 00:31:22 - src.core.universal_email_pusher - INFO - ✅ 初始化完成，已标记 47 封邮件为已见
2025-08-18 00:31:22 - src.gui.main_window - INFO - ✅ 自动刷新已启动，间隔: 50秒
2025-08-18 00:31:22 - src.gui.main_window - INFO - 🚀 邮件告警服务启动完成
2025-08-18 00:31:22 - src.email_handler.simple_email_client - DEBUG - 找到 47 封邮件
2025-08-18 00:31:27 - src.email_handler.simple_email_client - INFO - 成功获取 47 封邮件
2025-08-18 00:31:27 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 47封
2025-08-18 00:31:27 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 47 封邮件
2025-08-18 00:31:27 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 47 封邮件
2025-08-18 00:31:27 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 47 封邮件
2025-08-18 00:31:29 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 00:31:29 - src.gui.main_window - INFO - 🛑 自动刷新已停止
2025-08-18 00:31:29 - src.gui.main_window - INFO - 应用程序已关闭
2025-08-18 01:00:11 - src.gui.main_window - INFO - 邮件客户端创建成功: <EMAIL>
2025-08-18 01:00:11 - src.pusher.dingtalk_pusher - INFO - 📤 简洁推送器初始化，Webhook数量: 1
2025-08-18 01:00:11 - src.gui.main_window - INFO - 推送器创建成功，Webhook数量: 1
2025-08-18 01:00:11 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 47个
2025-08-18 01:00:11 - src.core.universal_email_pusher - INFO - 📧 邮件告警推送器启动
2025-08-18 01:00:11 - src.core.universal_email_pusher - INFO - 🎯 关键字: ['HZ', 'hz', '告警汇总']
2025-08-18 01:00:11 - src.core.universal_email_pusher - INFO - 📬 邮件客户端: 1个
2025-08-18 01:00:11 - src.core.universal_email_pusher - INFO - 💾 已加载 47 个已见邮件UID
2025-08-18 01:00:11 - src.gui.main_window - INFO - ✅ 邮件告警服务初始化完成
2025-08-18 01:00:12 - src.core.universal_email_pusher - INFO - 🔧 初始化邮件推送器...
2025-08-18 01:00:12 - src.core.universal_email_pusher - DEBUG - 邮件客户端未连接，尝试重连: <EMAIL>
2025-08-18 01:00:12 - src.email_handler.simple_email_client - INFO - 尝试连接到邮箱: <EMAIL> (尝试 1/3)
2025-08-18 01:00:12 - src.email_handler.simple_email_client - INFO - 成功连接到邮箱: <EMAIL>
2025-08-18 01:00:13 - src.email_handler.simple_email_client - DEBUG - 找到 48 封邮件
2025-08-18 01:00:18 - src.email_handler.simple_email_client - INFO - 成功获取 48 封邮件
2025-08-18 01:00:18 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 48封
2025-08-18 01:00:18 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 48 封邮件
2025-08-18 01:00:18 - src.core.universal_email_pusher - INFO - 📧 发现邮箱中有 48 封邮件
2025-08-18 01:00:18 - src.core.universal_email_pusher - INFO - ✅ 初始化完成，已标记 48 封邮件为已见
2025-08-18 01:00:18 - src.gui.main_window - INFO - ✅ 自动刷新已启动，间隔: 50秒
2025-08-18 01:00:18 - src.gui.main_window - INFO - 🚀 邮件告警服务启动完成
2025-08-18 01:00:18 - src.email_handler.simple_email_client - DEBUG - 找到 48 封邮件
2025-08-18 01:00:22 - src.email_handler.simple_email_client - INFO - 成功获取 48 封邮件
2025-08-18 01:00:22 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 48封
2025-08-18 01:00:22 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 48 封邮件
2025-08-18 01:00:22 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 48 封邮件
2025-08-18 01:00:22 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 48 封邮件
2025-08-18 01:01:08 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:01:08 - src.email_handler.simple_email_client - DEBUG - 找到 48 封邮件
2025-08-18 01:01:13 - src.email_handler.simple_email_client - INFO - 成功获取 48 封邮件
2025-08-18 01:01:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 48封
2025-08-18 01:01:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 48 封邮件
2025-08-18 01:01:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.5秒)
2025-08-18 01:01:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:01:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 48
2025-08-18 01:01:13 - src.email_handler.simple_email_client - DEBUG - 找到 48 封邮件
2025-08-18 01:01:18 - src.email_handler.simple_email_client - INFO - 成功获取 48 封邮件
2025-08-18 01:01:18 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 48封
2025-08-18 01:01:18 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 48 封邮件
2025-08-18 01:01:18 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 48 封邮件
2025-08-18 01:01:18 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 48 封邮件
2025-08-18 01:01:58 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:01:58 - src.email_handler.simple_email_client - DEBUG - 找到 48 封邮件
2025-08-18 01:02:04 - src.email_handler.simple_email_client - INFO - 成功获取 48 封邮件
2025-08-18 01:02:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 48封
2025-08-18 01:02:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 48 封邮件
2025-08-18 01:02:04 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.0秒)
2025-08-18 01:02:04 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:02:04 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 48
2025-08-18 01:02:04 - src.email_handler.simple_email_client - DEBUG - 找到 48 封邮件
2025-08-18 01:02:09 - src.email_handler.simple_email_client - INFO - 成功获取 48 封邮件
2025-08-18 01:02:09 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 48封
2025-08-18 01:02:09 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 48 封邮件
2025-08-18 01:02:09 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 48 封邮件
2025-08-18 01:02:09 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 48 封邮件
2025-08-18 01:02:48 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:02:48 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:02:53 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:02:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:02:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:02:53 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 01:02:00
2025-08-18 01:02:53 - src.core.universal_email_pusher - DEBUG - ⏭️ 跳过: 汇总-0818 01:02:00 (不匹配关键字)
2025-08-18 01:02:53 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.2秒)
2025-08-18 01:02:53 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 0封
2025-08-18 01:02:53 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 49
2025-08-18 01:02:53 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:02:58 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:02:58 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:02:58 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:02:58 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 49 封邮件
2025-08-18 01:02:58 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 49 封邮件
2025-08-18 01:03:32 - src.gui.config_dialog - INFO - 配置加载完成
2025-08-18 01:03:38 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:03:38 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:03:43 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:03:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:03:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:03:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.1秒)
2025-08-18 01:03:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:03:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 49
2025-08-18 01:03:43 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:03:47 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:03:47 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:03:47 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:03:48 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 49 封邮件
2025-08-18 01:03:48 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 49 封邮件
2025-08-18 01:03:48 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:03:48 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:03:48 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:03:48 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:03:48 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:03:48 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:03:48 - src.gui.main_window - INFO - 🛑 自动刷新已停止
2025-08-18 01:03:48 - src.gui.main_window - INFO - 邮件客户端创建成功: <EMAIL>
2025-08-18 01:03:48 - src.pusher.dingtalk_pusher - INFO - 📤 简洁推送器初始化，Webhook数量: 1
2025-08-18 01:03:48 - src.gui.main_window - INFO - 推送器创建成功，Webhook数量: 1
2025-08-18 01:03:48 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 49个
2025-08-18 01:03:48 - src.core.universal_email_pusher - INFO - 📧 邮件告警推送器启动
2025-08-18 01:03:48 - src.core.universal_email_pusher - INFO - 🎯 关键字: ['HZ', 'hz', '告警汇总']
2025-08-18 01:03:48 - src.core.universal_email_pusher - INFO - 📬 邮件客户端: 1个
2025-08-18 01:03:48 - src.core.universal_email_pusher - INFO - 💾 已加载 49 个已见邮件UID
2025-08-18 01:03:48 - src.email_handler.simple_email_client - DEBUG - 已断开邮箱连接: <EMAIL>
2025-08-18 01:03:48 - src.gui.main_window - INFO - ✅ 邮件告警服务初始化完成
2025-08-18 01:03:49 - src.core.universal_email_pusher - INFO - 🔧 初始化邮件推送器...
2025-08-18 01:03:49 - src.core.universal_email_pusher - DEBUG - 邮件客户端未连接，尝试重连: <EMAIL>
2025-08-18 01:03:49 - src.email_handler.simple_email_client - INFO - 尝试连接到邮箱: <EMAIL> (尝试 1/3)
2025-08-18 01:03:49 - src.email_handler.simple_email_client - INFO - 成功连接到邮箱: <EMAIL>
2025-08-18 01:03:49 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:03:54 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:03:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:03:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:03:54 - src.core.universal_email_pusher - INFO - 📧 发现邮箱中有 49 封邮件
2025-08-18 01:03:54 - src.core.universal_email_pusher - INFO - ✅ 初始化完成，已标记 49 封邮件为已见
2025-08-18 01:03:54 - src.gui.main_window - INFO - ✅ 自动刷新已启动，间隔: 50秒
2025-08-18 01:03:54 - src.gui.main_window - INFO - 🚀 邮件告警服务启动完成
2025-08-18 01:03:54 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:04:01 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:04:01 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:04:01 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:04:01 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 49 封邮件
2025-08-18 01:04:01 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 49 封邮件
2025-08-18 01:04:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:04:44 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:04:50 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:04:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:04:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:04:50 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.4秒)
2025-08-18 01:04:50 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:04:50 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 49
2025-08-18 01:04:50 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:04:55 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:04:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:04:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:04:55 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 49 封邮件
2025-08-18 01:04:55 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 49 封邮件
2025-08-18 01:05:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:05:34 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:05:40 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:05:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:05:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:05:40 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.7秒)
2025-08-18 01:05:40 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:05:40 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 49
2025-08-18 01:05:40 - src.email_handler.simple_email_client - DEBUG - 找到 49 封邮件
2025-08-18 01:05:46 - src.email_handler.simple_email_client - INFO - 成功获取 49 封邮件
2025-08-18 01:05:46 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 49封
2025-08-18 01:05:46 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 49 封邮件
2025-08-18 01:05:46 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 49 封邮件
2025-08-18 01:05:46 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 49 封邮件
2025-08-18 01:06:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:06:25 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:06:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:06:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:06:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:06:30 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 01:06:05
2025-08-18 01:06:30 - src.core.universal_email_pusher - DEBUG - ⏭️ 跳过: 汇总-0818 01:06:05 (不匹配关键字)
2025-08-18 01:06:30 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.3秒)
2025-08-18 01:06:30 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 0封
2025-08-18 01:06:30 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 50
2025-08-18 01:06:30 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:06:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:06:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:06:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:06:34 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:06:34 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:07:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:07:15 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:07:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:07:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:07:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:07:21 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 01:07:21 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:07:21 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 50
2025-08-18 01:07:21 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:07:27 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:07:27 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:07:27 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:07:27 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:07:27 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:08:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:08:04 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:08:11 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:08:11 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:08:11 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:08:11 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.8秒)
2025-08-18 01:08:11 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:08:11 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 50
2025-08-18 01:08:11 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:08:18 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:08:18 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:08:18 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:08:18 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:08:18 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:08:23 - src.gui.config_dialog - INFO - 配置保存成功
2025-08-18 01:08:33 - src.gui.main_window - INFO - 🔄 用户手动刷新邮件
2025-08-18 01:08:33 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:08:33 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:08:38 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:08:38 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:08:38 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:08:38 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.5秒)
2025-08-18 01:08:38 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:08:38 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 50
2025-08-18 01:08:38 - src.gui.main_window - INFO - ⏰ 倒计时已重置
2025-08-18 01:08:38 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:08:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:08:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:08:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:08:44 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:08:44 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:08:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:08:54 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:09:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:09:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:09:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:09:00 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.0秒)
2025-08-18 01:09:00 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:09:00 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 50
2025-08-18 01:09:01 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:09:07 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:09:07 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:09:07 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:09:07 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:09:07 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:09:37 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:09:37 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:09:37 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:09:37 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:09:37 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:09:37 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:09:37 - src.gui.main_window - INFO - 🛑 自动刷新已停止
2025-08-18 01:09:37 - src.gui.main_window - INFO - 邮件客户端创建成功: <EMAIL>
2025-08-18 01:09:37 - src.pusher.dingtalk_pusher - INFO - 📤 简洁推送器初始化，Webhook数量: 1
2025-08-18 01:09:37 - src.gui.main_window - INFO - 推送器创建成功，Webhook数量: 1
2025-08-18 01:09:37 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 50个
2025-08-18 01:09:37 - src.core.universal_email_pusher - INFO - 📧 邮件告警推送器启动
2025-08-18 01:09:37 - src.core.universal_email_pusher - INFO - 🎯 关键字: ['HZ', 'hz', '告警汇总', '汇总']
2025-08-18 01:09:37 - src.core.universal_email_pusher - INFO - 📬 邮件客户端: 1个
2025-08-18 01:09:37 - src.core.universal_email_pusher - INFO - 💾 已加载 50 个已见邮件UID
2025-08-18 01:09:37 - src.email_handler.simple_email_client - DEBUG - 已断开邮箱连接: <EMAIL>
2025-08-18 01:09:37 - src.gui.main_window - INFO - ✅ 邮件告警服务初始化完成
2025-08-18 01:09:38 - src.core.universal_email_pusher - INFO - 🔧 初始化邮件推送器...
2025-08-18 01:09:38 - src.core.universal_email_pusher - DEBUG - 邮件客户端未连接，尝试重连: <EMAIL>
2025-08-18 01:09:38 - src.email_handler.simple_email_client - INFO - 尝试连接到邮箱: <EMAIL> (尝试 1/3)
2025-08-18 01:09:39 - src.email_handler.simple_email_client - INFO - 成功连接到邮箱: <EMAIL>
2025-08-18 01:09:39 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:09:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:09:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:09:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:09:44 - src.core.universal_email_pusher - INFO - 📧 发现邮箱中有 50 封邮件
2025-08-18 01:09:44 - src.core.universal_email_pusher - INFO - ✅ 初始化完成，已标记 50 封邮件为已见
2025-08-18 01:09:44 - src.gui.main_window - INFO - ✅ 自动刷新已启动，间隔: 50秒
2025-08-18 01:09:44 - src.gui.main_window - INFO - 🚀 邮件告警服务启动完成
2025-08-18 01:09:44 - src.email_handler.simple_email_client - DEBUG - 找到 50 封邮件
2025-08-18 01:09:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:09:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:09:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:09:51 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:09:51 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:09:51 - src.gui.config_dialog - INFO - 配置保存成功
2025-08-18 01:10:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:10:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:10:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:10:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:10:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:10:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:10:40 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 01:10:05
2025-08-18 01:10:40 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 01:10:05
2025-08-18 01:10:40 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 01:10:05 (长度: 3589)
2025-08-18 01:10:40 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 01:10:05
2025-08-18 01:10:40 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 01:10:05
2025-08-18 01:10:41 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_011005_p1.png (344577 bytes)
2025-08-18 01:10:41 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 01:10:41 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 01:10:42 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 01:10:42 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_011005_p1.png
2025-08-18 01:10:42 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 01:10:05, 成功推送 1 张图片
2025-08-18 01:10:42 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 01:10:05
2025-08-18 01:10:42 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 01:10:05
2025-08-18 01:10:42 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.9秒)
2025-08-18 01:10:42 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 01:10:42 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:10:42 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:10:42 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:10:47 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:10:47 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:10:47 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:10:47 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:10:47 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:11:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:11:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:11:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:11:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:11:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:11:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:11:29 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.5秒)
2025-08-18 01:11:29 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:11:29 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:11:30 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:11:30 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:11:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:11:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:11:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:11:35 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:11:35 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:12:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:12:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:12:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:12:19 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:12:19 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:12:19 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:12:19 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.3秒)
2025-08-18 01:12:19 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:12:19 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:12:19 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:12:19 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:12:25 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:12:25 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:12:25 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:12:25 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:12:25 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:13:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:13:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:13:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:13:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:13:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:13:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:13:10 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.7秒)
2025-08-18 01:13:10 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:13:10 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:13:10 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:13:10 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:13:15 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:13:15 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:13:15 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:13:15 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:13:15 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:13:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:13:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:13:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:14:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:14:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:14:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:14:00 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.0秒)
2025-08-18 01:14:00 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:14:00 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:14:00 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:14:00 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:14:06 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:14:06 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:14:06 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:14:06 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:14:06 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:14:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:14:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:14:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:14:49 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:14:49 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:14:49 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:14:49 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.0秒)
2025-08-18 01:14:49 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:14:49 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:14:49 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:14:49 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:14:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:14:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:14:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:14:55 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:14:55 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:15:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:15:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:15:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:15:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:15:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:15:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:15:40 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:15:40 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:15:40 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:15:40 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:15:40 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:15:46 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:15:46 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:15:46 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:15:46 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:15:46 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:16:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:16:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:16:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:16:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:16:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:16:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:16:29 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.4秒)
2025-08-18 01:16:29 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:16:29 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:16:29 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:16:29 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:16:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:16:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:16:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:16:34 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:16:34 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:17:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:17:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:17:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:17:20 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:17:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:17:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:17:20 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.6秒)
2025-08-18 01:17:20 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:17:20 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:17:20 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:17:20 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:17:25 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:17:25 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:17:25 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:17:26 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:17:26 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:18:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:18:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:18:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:18:11 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:18:11 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:18:11 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:18:11 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 01:18:11 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:18:11 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:18:11 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 51个
2025-08-18 01:18:11 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:18:11 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:18:17 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:18:17 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:18:17 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:18:17 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:18:17 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:18:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:18:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:18:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:18:59 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:18:59 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:18:59 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:18:59 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.5秒)
2025-08-18 01:18:59 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:18:59 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:19:00 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:19:00 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:19:05 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:19:05 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:19:05 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:19:05 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:19:05 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:19:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:19:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:19:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:19:50 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:19:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:19:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:19:50 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:19:50 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:19:50 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:19:50 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:19:50 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:19:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:19:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:19:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:19:55 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:19:55 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:20:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:20:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:20:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:20:39 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:20:39 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:20:39 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:20:39 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.1秒)
2025-08-18 01:20:39 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:20:39 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:20:39 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:20:39 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:20:45 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:20:45 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:20:45 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:20:46 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:20:46 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:21:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:21:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:21:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:21:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:21:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:21:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:21:29 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.2秒)
2025-08-18 01:21:29 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:21:29 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:21:29 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:21:29 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:21:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:21:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:21:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:21:34 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:21:34 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:22:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:22:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:22:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:22:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:22:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:22:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:22:21 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 01:22:21 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:22:21 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:22:21 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:22:21 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:22:28 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:22:28 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:22:28 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:22:29 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:22:29 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:23:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:23:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:23:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:23:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:23:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:23:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:23:10 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.1秒)
2025-08-18 01:23:10 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:23:10 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:23:10 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:23:10 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:23:16 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:23:16 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:23:16 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:23:17 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:23:17 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:23:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:23:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:23:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:24:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:24:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:24:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:24:00 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:24:00 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:24:00 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:24:00 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:24:00 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:24:05 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:24:05 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:24:05 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:24:05 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:24:05 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:24:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:24:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:24:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:24:50 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:24:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:24:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:24:50 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.5秒)
2025-08-18 01:24:50 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:24:50 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:24:51 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:24:51 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:24:57 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:24:57 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:24:57 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:24:57 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:24:57 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:25:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:25:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:25:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:25:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:25:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:25:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:25:40 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.7秒)
2025-08-18 01:25:40 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:25:40 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:25:40 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:25:40 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:25:45 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:25:45 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:25:45 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:25:45 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:25:45 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:26:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:26:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:26:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:26:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:26:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:26:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:26:30 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.9秒)
2025-08-18 01:26:30 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:26:30 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:26:30 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 51个
2025-08-18 01:26:30 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:26:30 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:26:35 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:26:35 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:26:35 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:26:36 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:26:36 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:27:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:27:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:27:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:27:20 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:27:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:27:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:27:20 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.7秒)
2025-08-18 01:27:20 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:27:20 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:27:20 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:27:20 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:27:26 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:27:26 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:27:26 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:27:26 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:27:26 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:28:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:28:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:28:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:28:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:28:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:28:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:28:10 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.0秒)
2025-08-18 01:28:10 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:28:10 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:28:10 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:28:10 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:28:15 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:28:15 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:28:15 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:28:15 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:28:15 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:28:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:28:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:28:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:29:01 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:29:01 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:29:01 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:29:01 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.4秒)
2025-08-18 01:29:01 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:29:01 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:29:01 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:29:01 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:29:08 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:29:08 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:29:08 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:29:09 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:29:09 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:29:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:29:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:29:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:29:50 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:29:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:29:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:29:50 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.4秒)
2025-08-18 01:29:50 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:29:50 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:29:50 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:29:50 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:29:56 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:29:56 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:29:56 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:29:57 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:29:57 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:30:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:30:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:30:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:30:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:30:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:30:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:30:40 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.3秒)
2025-08-18 01:30:40 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:30:40 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:30:40 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:30:40 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:30:46 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:30:46 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:30:46 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:30:47 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:30:47 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:31:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:31:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:31:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:31:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:31:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:31:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:31:30 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.6秒)
2025-08-18 01:31:30 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:31:30 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:31:31 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:31:31 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:31:37 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:31:37 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:31:37 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:31:37 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:31:37 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:32:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:32:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:32:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:32:20 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:32:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:32:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:32:20 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.0秒)
2025-08-18 01:32:20 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:32:20 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:32:20 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:32:20 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:32:26 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:32:26 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:32:26 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:32:26 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:32:26 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:33:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:33:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:33:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:33:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:33:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:33:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:33:10 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.9秒)
2025-08-18 01:33:10 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:33:10 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:33:10 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:33:10 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:33:16 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:33:16 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:33:16 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:33:16 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:33:16 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:33:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:33:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:33:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:34:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:34:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:34:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:34:00 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.4秒)
2025-08-18 01:34:00 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:34:00 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:34:00 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:34:00 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:34:06 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:34:06 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:34:06 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:34:06 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:34:06 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:34:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:34:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:34:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:34:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:34:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:34:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:34:51 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.9秒)
2025-08-18 01:34:51 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:34:51 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:34:51 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 51个
2025-08-18 01:34:51 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:34:51 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:34:58 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:34:58 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:34:58 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:34:58 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:34:58 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:35:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:35:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:35:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:35:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:35:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:35:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:35:40 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:35:40 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:35:40 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:35:40 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:35:40 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:35:45 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:35:45 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:35:45 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:35:45 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:35:45 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:36:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:36:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:36:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:36:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:36:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:36:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:36:30 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:36:30 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:36:30 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:36:30 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:36:30 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:36:36 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:36:36 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:36:36 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:36:36 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:36:36 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:37:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:37:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:37:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:37:20 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:37:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:37:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:37:20 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.0秒)
2025-08-18 01:37:20 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:37:20 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:37:20 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:37:20 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:37:26 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:37:26 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:37:26 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:37:26 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:37:26 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:38:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:38:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:38:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:38:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:38:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:38:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:38:10 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:38:10 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:38:10 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:38:10 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:38:10 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:38:15 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:38:15 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:38:15 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:38:15 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:38:15 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:38:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:38:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:38:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:39:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:39:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:39:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:39:00 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.3秒)
2025-08-18 01:39:00 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:39:00 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:39:00 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:39:00 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:39:07 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:39:07 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:39:07 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:39:07 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:39:07 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:39:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:39:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:39:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:39:49 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:39:49 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:39:49 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:39:49 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.3秒)
2025-08-18 01:39:49 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:39:49 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:39:49 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:39:49 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:39:56 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:39:56 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:39:56 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:39:56 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:39:56 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:40:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:40:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:40:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:40:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:40:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:40:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:40:40 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.4秒)
2025-08-18 01:40:40 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:40:40 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:40:40 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:40:40 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:40:47 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:40:47 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:40:47 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:40:47 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:40:47 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:41:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:41:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:41:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:41:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:41:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:41:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:41:29 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.1秒)
2025-08-18 01:41:29 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:41:29 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:41:29 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:41:29 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:41:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:41:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:41:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:41:35 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:41:35 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:42:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:42:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:42:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:42:20 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:42:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:42:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:42:20 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:42:20 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:42:20 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:42:20 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:42:20 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:42:26 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:42:26 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:42:26 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:42:26 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:42:26 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:43:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:43:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:43:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:43:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:43:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:43:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:43:10 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.1秒)
2025-08-18 01:43:10 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:43:10 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:43:10 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 51个
2025-08-18 01:43:10 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:43:10 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:43:17 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:43:17 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:43:17 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:43:17 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:43:17 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:43:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:43:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:43:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:44:01 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:44:01 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:44:01 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:44:01 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 01:44:01 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:44:01 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:44:01 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:44:01 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:44:08 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:44:08 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:44:08 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:44:08 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:44:08 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:44:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:44:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:44:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:44:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:44:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:44:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:44:51 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 01:44:51 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:44:51 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:44:52 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:44:52 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:44:58 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:44:58 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:44:58 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:44:59 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:44:59 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:45:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:45:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:45:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:45:42 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:45:42 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:45:42 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:45:42 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.9秒)
2025-08-18 01:45:42 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:45:42 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:45:42 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:45:42 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:45:49 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:45:49 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:45:49 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:45:49 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:45:49 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:46:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:46:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:46:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:46:31 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:46:31 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:46:31 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:46:31 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.4秒)
2025-08-18 01:46:31 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:46:31 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:46:31 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:46:31 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:46:39 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:46:39 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:46:39 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:46:39 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:46:39 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:47:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:47:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:47:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:47:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:47:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:47:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:47:21 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.4秒)
2025-08-18 01:47:21 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:47:21 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:47:21 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:47:21 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:47:28 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:47:28 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:47:28 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:47:29 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:47:29 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:48:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:48:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:48:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:48:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:48:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:48:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:48:10 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.2秒)
2025-08-18 01:48:10 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:48:10 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:48:10 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:48:10 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:48:16 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:48:16 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:48:16 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:48:16 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:48:16 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:48:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:48:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:48:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:48:59 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:48:59 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:48:59 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:48:59 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.6秒)
2025-08-18 01:48:59 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:48:59 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:49:00 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:49:00 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:49:05 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:49:05 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:49:05 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:49:05 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:49:05 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:49:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:49:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:49:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:49:49 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:49:49 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:49:49 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:49:49 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.2秒)
2025-08-18 01:49:49 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:49:49 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:49:49 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:49:49 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:49:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:49:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:49:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:49:55 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:49:55 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:50:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:50:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:50:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:50:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:50:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:50:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:50:40 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.9秒)
2025-08-18 01:50:40 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:50:40 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:50:40 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:50:40 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:50:45 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:50:45 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:50:45 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:50:45 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:50:45 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:51:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:51:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:51:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:51:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:51:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:51:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:51:29 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.4秒)
2025-08-18 01:51:29 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:51:29 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:51:29 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 51个
2025-08-18 01:51:29 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:51:29 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:51:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:51:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:51:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:51:34 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:51:34 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:52:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:52:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:52:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:52:20 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:52:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:52:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:52:20 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.7秒)
2025-08-18 01:52:20 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:52:20 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:52:20 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:52:20 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:52:26 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:52:26 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:52:26 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:52:26 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:52:26 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:53:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:53:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:53:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:53:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:53:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:53:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:53:10 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.2秒)
2025-08-18 01:53:10 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:53:10 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:53:10 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:53:10 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:53:16 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:53:16 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:53:16 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:53:16 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:53:16 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:53:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:53:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:53:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:54:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:54:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:54:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:54:00 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:54:00 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:54:00 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:54:00 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:54:00 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:54:06 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:54:06 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:54:06 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:54:06 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:54:06 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:54:44 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:54:44 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:54:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:54:50 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:54:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:54:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:54:50 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.8秒)
2025-08-18 01:54:50 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:54:50 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:54:50 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:54:50 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:54:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:54:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:54:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:54:56 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:54:56 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:55:34 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:55:34 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:55:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:55:39 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:55:39 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:55:39 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:55:39 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.4秒)
2025-08-18 01:55:39 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:55:39 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:55:39 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:55:39 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:55:45 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:55:45 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:55:45 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:55:45 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:55:45 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:56:24 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:56:24 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:56:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:56:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:56:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:56:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:56:29 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.5秒)
2025-08-18 01:56:29 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:56:29 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:56:29 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:56:29 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:56:35 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:56:35 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:56:35 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:56:38 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:56:38 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:57:14 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:57:14 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:57:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:57:19 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:57:19 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:57:19 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:57:19 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 5.3秒)
2025-08-18 01:57:19 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:57:19 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:57:19 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:57:19 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:57:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:57:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:57:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:57:24 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:57:24 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:58:04 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:58:04 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:58:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:58:11 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:58:11 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:58:11 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:58:11 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.8秒)
2025-08-18 01:58:11 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:58:11 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:58:11 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:58:11 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:58:16 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:58:16 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:58:16 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:58:17 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:58:17 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:58:54 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 01:58:54 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:58:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:59:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:59:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:59:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:59:00 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.1秒)
2025-08-18 01:59:00 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 01:59:00 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 01:59:00 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 01:59:00 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 01:59:06 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 01:59:06 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 01:59:06 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 01:59:06 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 01:59:06 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 01:59:43 - src.config.config_manager - INFO - 配置文件保存成功
2025-08-18 01:59:43 - src.gui.main_window - INFO - 🛑 自动刷新已停止
2025-08-18 01:59:43 - src.gui.main_window - INFO - 应用程序已关闭
2025-08-18 01:59:43 - src.email_handler.simple_email_client - DEBUG - 已断开邮箱连接: <EMAIL>
2025-08-18 08:15:35 - src.gui.main_window - INFO - 邮件客户端创建成功: <EMAIL>
2025-08-18 08:15:35 - src.pusher.dingtalk_pusher - INFO - 📤 简洁推送器初始化，Webhook数量: 1
2025-08-18 08:15:35 - src.gui.main_window - INFO - 推送器创建成功，Webhook数量: 1
2025-08-18 08:15:35 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 51个
2025-08-18 08:15:35 - src.core.universal_email_pusher - INFO - 📧 邮件告警推送器启动
2025-08-18 08:15:35 - src.core.universal_email_pusher - INFO - 🎯 关键字: ['HZ', 'hz', '告警汇总', '汇总']
2025-08-18 08:15:35 - src.core.universal_email_pusher - INFO - 📬 邮件客户端: 1个
2025-08-18 08:15:35 - src.core.universal_email_pusher - INFO - 💾 已加载 51 个已见邮件UID
2025-08-18 08:15:35 - src.gui.main_window - INFO - ✅ 邮件告警服务初始化完成
2025-08-18 08:15:36 - src.core.universal_email_pusher - INFO - 🔧 初始化邮件推送器...
2025-08-18 08:15:36 - src.core.universal_email_pusher - DEBUG - 邮件客户端未连接，尝试重连: <EMAIL>
2025-08-18 08:15:36 - src.email_handler.simple_email_client - INFO - 尝试连接到邮箱: <EMAIL> (尝试 1/3)
2025-08-18 08:15:37 - src.email_handler.simple_email_client - INFO - 成功连接到邮箱: <EMAIL>
2025-08-18 08:15:37 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 08:15:37 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 08:15:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:15:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:15:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:15:43 - src.core.universal_email_pusher - INFO - 📧 发现邮箱中有 50 封邮件
2025-08-18 08:15:43 - src.core.universal_email_pusher - INFO - ✅ 初始化完成，已标记 51 封邮件为已见
2025-08-18 08:15:43 - src.gui.main_window - INFO - ✅ 自动刷新已启动，间隔: 50秒
2025-08-18 08:15:43 - src.gui.main_window - INFO - 🚀 邮件告警服务启动完成
2025-08-18 08:15:43 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 08:15:43 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 08:15:49 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:15:49 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:15:49 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:15:49 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:15:49 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:16:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:16:36 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 08:16:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 08:16:42 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:16:42 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:16:42 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:16:42 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.2秒)
2025-08-18 08:16:42 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:16:42 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 51
2025-08-18 08:16:42 - src.email_handler.simple_email_client - DEBUG - 找到 51 封邮件
2025-08-18 08:16:42 - src.email_handler.simple_email_client - INFO - 邮件数量过多(51)，只获取最新的50封
2025-08-18 08:16:48 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:16:48 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:16:48 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:16:48 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:16:48 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:17:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:17:26 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:17:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:17:35 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:17:35 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:17:35 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:17:35 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 08:16:42
2025-08-18 08:17:35 - src.core.universal_email_pusher - DEBUG - ⏭️ 跳过: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 08:16:42 (不匹配关键字)
2025-08-18 08:17:35 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.7秒)
2025-08-18 08:17:35 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 0封
2025-08-18 08:17:35 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 52
2025-08-18 08:17:35 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:17:35 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:17:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:17:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:17:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:17:40 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:17:40 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:18:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:18:16 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:18:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:18:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:18:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:18:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:18:24 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.1秒)
2025-08-18 08:18:24 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:18:24 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 52
2025-08-18 08:18:24 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:18:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:18:31 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:18:31 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:18:31 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:18:31 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:18:31 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:19:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:19:06 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:19:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:19:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:19:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:19:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:19:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 08:19:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:19:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 52
2025-08-18 08:19:13 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:19:13 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:19:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:19:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:19:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:19:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:19:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:19:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:19:56 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:19:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:20:04 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:20:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:20:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:20:04 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.5秒)
2025-08-18 08:20:04 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:20:04 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 52
2025-08-18 08:20:05 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:20:05 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:20:14 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:20:14 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:20:14 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:20:14 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:20:14 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:20:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:20:46 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:20:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:20:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:20:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:20:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:20:54 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.8秒)
2025-08-18 08:20:54 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:20:54 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 52
2025-08-18 08:20:54 - src.email_handler.simple_email_client - DEBUG - 找到 52 封邮件
2025-08-18 08:20:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(52)，只获取最新的50封
2025-08-18 08:21:02 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:21:02 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:21:02 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:21:02 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:21:02 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:21:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:21:36 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:21:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:21:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:21:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:21:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:21:44 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 告警汇总-2025年08月18日
2025-08-18 08:21:44 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '告警汇总': 告警汇总-2025年08月18日
2025-08-18 08:21:45 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 告警汇总-2025年08月18日 (长度: 2663)
2025-08-18 08:21:45 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 告警汇总-2025年08月18日
2025-08-18 08:21:45 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 告警汇总-2025年08月18日
2025-08-18 08:21:46 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_082046_p1.png (288427 bytes)
2025-08-18 08:21:46 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 08:21:46 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 08:21:47 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 08:21:47 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_082046_p1.png
2025-08-18 08:21:47 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 告警汇总-2025年08月18日, 成功推送 1 张图片
2025-08-18 08:21:47 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 告警汇总-2025年08月18日
2025-08-18 08:21:47 - src.core.universal_email_pusher - INFO - 📤 推送: 告警汇总-2025年08月18日
2025-08-18 08:21:47 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 10.8秒)
2025-08-18 08:21:47 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 08:21:47 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:21:47 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:21:47 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:21:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:21:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:21:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:21:55 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:21:55 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:22:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:22:26 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:22:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:22:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:22:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:22:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:22:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.7秒)
2025-08-18 08:22:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:22:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:22:33 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:22:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:22:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:22:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:22:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:22:40 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:22:40 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:23:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:23:16 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:23:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:23:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:23:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:23:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:23:24 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.8秒)
2025-08-18 08:23:24 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:23:24 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:23:24 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:23:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:23:32 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:23:32 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:23:32 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:23:32 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:23:32 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:24:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:24:06 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:24:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:24:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:24:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:24:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:24:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 08:24:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:24:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:24:13 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 53个
2025-08-18 08:24:14 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:24:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:24:20 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:24:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:24:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:24:20 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:24:20 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:24:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:24:56 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:24:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:25:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:25:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:25:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:25:03 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 08:25:03 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:25:03 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:25:03 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:25:03 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:25:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:25:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:25:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:25:11 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:25:11 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:25:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:25:46 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:25:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:25:53 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:25:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:25:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:25:53 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.1秒)
2025-08-18 08:25:53 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:25:53 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:25:53 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:25:53 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:26:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:26:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:26:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:26:01 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:26:01 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:26:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:26:36 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:26:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:26:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:26:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:26:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:26:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.4秒)
2025-08-18 08:26:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:26:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:26:44 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:26:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:26:50 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:26:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:26:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:26:50 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:26:50 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:27:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:27:26 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:27:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:27:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:27:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:27:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:27:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.9秒)
2025-08-18 08:27:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:27:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:27:33 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:27:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:27:39 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:27:39 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:27:39 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:27:39 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:27:39 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:28:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:28:16 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:28:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:28:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:28:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:28:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:28:23 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 08:28:23 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:28:23 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:28:23 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:28:23 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:28:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:28:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:28:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:28:29 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:28:29 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:29:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:29:06 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:29:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:29:12 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:29:12 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:29:12 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:29:12 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.3秒)
2025-08-18 08:29:12 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:29:12 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:29:12 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:29:12 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:29:19 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:29:19 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:29:19 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:29:19 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:29:19 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:29:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:29:56 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:29:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:30:02 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:30:02 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:30:02 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:30:02 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.0秒)
2025-08-18 08:30:02 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:30:02 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 53
2025-08-18 08:30:02 - src.email_handler.simple_email_client - DEBUG - 找到 53 封邮件
2025-08-18 08:30:02 - src.email_handler.simple_email_client - INFO - 邮件数量过多(53)，只获取最新的50封
2025-08-18 08:30:09 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:30:09 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:30:09 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:30:09 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:30:09 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:30:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:30:46 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:30:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:30:53 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:30:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:30:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:30:53 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 08:30:40
2025-08-18 08:30:53 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 08:30:40
2025-08-18 08:30:54 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 08:30:40 (长度: 3589)
2025-08-18 08:30:54 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 08:30:40
2025-08-18 08:30:54 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 08:30:40
2025-08-18 08:30:55 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_083040_p1.png (343634 bytes)
2025-08-18 08:30:55 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 08:30:55 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 08:30:56 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 08:30:56 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_083040_p1.png
2025-08-18 08:30:56 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 08:30:40, 成功推送 1 张图片
2025-08-18 08:30:56 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 08:30:40
2025-08-18 08:30:56 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 08:30:40
2025-08-18 08:30:56 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 9.9秒)
2025-08-18 08:30:56 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 08:30:56 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 54
2025-08-18 08:30:56 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:30:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:31:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:31:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:31:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:31:03 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:31:03 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:31:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:31:36 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:31:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:31:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:31:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:31:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:31:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.5秒)
2025-08-18 08:31:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:31:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 54
2025-08-18 08:31:43 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:31:43 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:31:49 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:31:49 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:31:49 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:31:49 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:31:49 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:32:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:32:26 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:32:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:32:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:32:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:32:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:32:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.1秒)
2025-08-18 08:32:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:32:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 54
2025-08-18 08:32:33 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 54个
2025-08-18 08:32:33 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:32:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:32:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:32:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:32:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:32:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:32:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:33:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:33:16 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:33:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:33:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:33:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:33:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:33:24 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.1秒)
2025-08-18 08:33:24 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:33:24 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 54
2025-08-18 08:33:24 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:33:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:33:32 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:33:32 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:33:32 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:33:32 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:33:32 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:34:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:34:06 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:34:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:34:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:34:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:34:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:34:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.8秒)
2025-08-18 08:34:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:34:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 54
2025-08-18 08:34:13 - src.email_handler.simple_email_client - DEBUG - 找到 54 封邮件
2025-08-18 08:34:13 - src.email_handler.simple_email_client - INFO - 邮件数量过多(54)，只获取最新的50封
2025-08-18 08:34:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:34:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:34:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:34:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:34:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:34:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:34:56 - src.email_handler.simple_email_client - DEBUG - 找到 55 封邮件
2025-08-18 08:34:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(55)，只获取最新的50封
2025-08-18 08:35:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:35:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:35:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:35:03 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 08:34:34
2025-08-18 08:35:03 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 08:34:34
2025-08-18 08:35:04 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 08:34:34 (长度: 3589)
2025-08-18 08:35:04 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 08:34:34
2025-08-18 08:35:04 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 08:34:34
2025-08-18 08:35:04 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_083434_p1.png (345369 bytes)
2025-08-18 08:35:04 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 08:35:04 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 08:35:05 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 08:35:05 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_083434_p1.png
2025-08-18 08:35:05 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 08:34:34, 成功推送 1 张图片
2025-08-18 08:35:05 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 08:34:34
2025-08-18 08:35:05 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 08:34:34
2025-08-18 08:35:05 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 9.3秒)
2025-08-18 08:35:05 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 08:35:05 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 55
2025-08-18 08:35:06 - src.email_handler.simple_email_client - DEBUG - 找到 55 封邮件
2025-08-18 08:35:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(55)，只获取最新的50封
2025-08-18 08:35:14 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:35:14 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:35:14 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:35:14 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:35:14 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:35:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:35:46 - src.email_handler.simple_email_client - DEBUG - 找到 55 封邮件
2025-08-18 08:35:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(55)，只获取最新的50封
2025-08-18 08:35:53 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:35:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:35:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:35:53 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 08:35:53 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:35:53 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 55
2025-08-18 08:35:53 - src.email_handler.simple_email_client - DEBUG - 找到 55 封邮件
2025-08-18 08:35:53 - src.email_handler.simple_email_client - INFO - 邮件数量过多(55)，只获取最新的50封
2025-08-18 08:36:01 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:36:01 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:36:01 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:36:01 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:36:01 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:36:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:36:36 - src.email_handler.simple_email_client - DEBUG - 找到 55 封邮件
2025-08-18 08:36:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(55)，只获取最新的50封
2025-08-18 08:36:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:36:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:36:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:36:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 08:36:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:36:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 55
2025-08-18 08:36:43 - src.email_handler.simple_email_client - DEBUG - 找到 55 封邮件
2025-08-18 08:36:43 - src.email_handler.simple_email_client - INFO - 邮件数量过多(55)，只获取最新的50封
2025-08-18 08:36:50 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:36:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:36:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:36:50 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:36:50 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:37:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:37:26 - src.email_handler.simple_email_client - DEBUG - 找到 55 封邮件
2025-08-18 08:37:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(55)，只获取最新的50封
2025-08-18 08:37:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:37:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:37:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:37:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.4秒)
2025-08-18 08:37:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:37:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 55
2025-08-18 08:37:34 - src.email_handler.simple_email_client - DEBUG - 找到 55 封邮件
2025-08-18 08:37:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(55)，只获取最新的50封
2025-08-18 08:37:41 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:37:41 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:37:41 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:37:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:37:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:38:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:38:16 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:38:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:38:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:38:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:38:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:38:23 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 08:37:16
2025-08-18 08:38:23 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 08:37:16
2025-08-18 08:38:24 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 08:37:16 (长度: 2663)
2025-08-18 08:38:24 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 08:37:16
2025-08-18 08:38:24 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 08:37:16
2025-08-18 08:38:24 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_083716_p1.png (244246 bytes)
2025-08-18 08:38:24 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 08:38:24 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 08:38:25 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 08:38:25 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_083716_p1.png
2025-08-18 08:38:25 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 08:37:16, 成功推送 1 张图片
2025-08-18 08:38:25 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 08:37:16
2025-08-18 08:38:25 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 08:37:16
2025-08-18 08:38:25 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 9.1秒)
2025-08-18 08:38:25 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 08:38:25 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:38:25 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:38:25 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:38:32 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:38:32 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:38:32 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:38:32 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:38:32 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:39:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:39:06 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:39:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:39:12 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:39:12 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:39:12 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:39:12 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.4秒)
2025-08-18 08:39:12 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:39:12 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:39:13 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:39:13 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:39:19 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:39:19 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:39:19 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:39:19 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:39:19 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:39:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:39:56 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:39:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:40:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:40:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:40:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:40:03 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.6秒)
2025-08-18 08:40:03 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:40:03 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:40:03 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:40:03 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:40:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:40:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:40:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:40:10 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:40:10 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:40:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:40:46 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:40:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:40:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:40:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:40:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:40:54 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.1秒)
2025-08-18 08:40:54 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:40:54 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:40:54 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 56个
2025-08-18 08:40:54 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:40:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:41:02 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:41:02 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:41:02 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:41:02 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:41:02 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:41:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:41:36 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:41:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:41:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:41:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:41:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:41:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 08:41:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:41:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:41:43 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:41:43 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:41:50 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:41:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:41:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:41:50 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:41:50 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:42:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:42:26 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:42:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:42:32 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:42:32 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:42:32 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:42:32 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.5秒)
2025-08-18 08:42:32 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:42:32 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:42:33 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:42:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:42:39 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:42:39 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:42:39 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:42:39 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:42:39 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:43:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:43:16 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:43:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:43:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:43:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:43:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:43:24 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.7秒)
2025-08-18 08:43:24 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:43:24 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:43:24 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:43:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:43:31 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:43:31 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:43:31 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:43:31 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:43:31 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:44:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:44:06 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:44:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:44:14 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:44:14 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:44:14 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:44:14 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.6秒)
2025-08-18 08:44:14 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:44:14 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:44:14 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:44:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:44:22 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:44:22 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:44:22 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:44:22 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:44:22 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:44:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:44:56 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:44:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:45:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:45:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:45:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:45:03 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 08:45:03 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:45:03 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:45:03 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:45:03 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:45:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:45:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:45:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:45:10 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:45:10 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:45:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:45:46 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:45:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:45:53 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:45:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:45:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:45:53 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 08:45:53 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:45:53 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:45:53 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:45:53 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:46:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:46:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:46:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:46:00 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:46:00 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:46:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:46:36 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:46:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:46:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:46:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:46:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:46:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.8秒)
2025-08-18 08:46:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:46:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:46:43 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:46:43 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:46:49 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:46:49 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:46:49 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:46:49 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:46:49 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:47:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:47:26 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:47:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:47:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:47:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:47:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:47:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.8秒)
2025-08-18 08:47:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:47:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:47:33 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:47:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:47:39 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:47:39 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:47:39 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:47:39 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:47:39 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:48:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:48:16 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:48:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:48:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:48:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:48:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:48:23 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.8秒)
2025-08-18 08:48:23 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:48:23 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:48:23 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:48:23 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:48:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:48:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:48:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:48:29 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:48:29 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:49:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:49:06 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:49:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:49:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:49:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:49:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:49:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.8秒)
2025-08-18 08:49:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:49:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:49:13 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 56个
2025-08-18 08:49:13 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:49:13 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:49:19 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:49:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:49:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:49:20 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:49:20 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:49:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:49:56 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:49:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:50:04 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:50:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:50:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:50:04 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.3秒)
2025-08-18 08:50:04 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:50:04 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:50:05 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:50:05 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:50:12 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:50:12 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:50:12 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:50:12 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:50:12 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:50:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:50:46 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:50:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:50:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:50:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:50:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:50:54 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.2秒)
2025-08-18 08:50:54 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:50:54 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:50:54 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:50:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:51:02 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:51:02 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:51:02 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:51:02 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:51:02 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:51:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:51:36 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:51:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:51:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:51:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:51:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:51:44 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.2秒)
2025-08-18 08:51:44 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:51:44 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:51:44 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:51:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:51:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:51:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:51:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:51:51 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:51:51 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:52:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:52:26 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:52:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:52:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:52:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:52:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:52:34 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.8秒)
2025-08-18 08:52:34 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:52:34 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:52:34 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:52:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:52:42 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:52:42 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:52:42 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:52:42 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:52:42 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:53:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:53:16 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:53:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:53:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:53:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:53:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:53:23 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.1秒)
2025-08-18 08:53:23 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:53:23 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:53:23 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:53:23 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:53:29 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:53:29 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:53:29 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:53:29 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:53:29 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:54:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:54:06 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:54:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:54:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:54:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:54:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:54:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 08:54:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:54:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:54:13 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:54:13 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:54:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:54:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:54:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:54:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:54:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:54:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:54:56 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:54:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:55:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:55:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:55:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:55:03 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.6秒)
2025-08-18 08:55:03 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:55:03 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:55:03 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:55:03 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:55:10 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:55:10 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:55:10 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:55:10 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:55:10 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:55:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:55:46 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:55:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:55:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:55:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:55:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:55:54 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.1秒)
2025-08-18 08:55:54 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:55:54 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:55:54 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:55:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:56:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:56:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:56:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:56:03 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:56:03 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:56:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:56:36 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:56:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:56:45 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:56:45 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:56:45 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:56:45 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 9.3秒)
2025-08-18 08:56:45 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:56:45 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:56:45 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:56:45 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:56:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:56:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:56:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:56:54 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:56:54 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:57:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:57:26 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:57:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:57:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:57:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:57:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:57:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.4秒)
2025-08-18 08:57:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:57:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:57:33 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 56个
2025-08-18 08:57:34 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:57:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:57:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:57:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:57:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:57:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:57:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:58:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:58:16 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:58:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:58:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:58:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:58:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:58:24 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.7秒)
2025-08-18 08:58:24 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:58:24 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:58:24 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:58:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:58:31 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:58:31 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:58:31 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:58:31 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:58:31 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:59:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:59:06 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:59:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:59:14 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:59:14 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:59:14 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:59:14 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.1秒)
2025-08-18 08:59:14 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 08:59:14 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 08:59:14 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:59:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 08:59:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 08:59:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 08:59:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 08:59:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 08:59:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 08:59:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 08:59:56 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 08:59:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:00:06 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:00:06 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:00:06 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:00:06 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 10.3秒)
2025-08-18 09:00:06 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:00:06 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:00:07 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:00:07 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:00:15 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:00:15 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:00:15 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:00:15 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:00:15 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:00:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:00:46 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:00:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:00:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:00:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:00:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:00:54 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.8秒)
2025-08-18 09:00:54 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:00:54 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:00:54 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:00:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:01:02 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:01:02 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:01:02 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:01:02 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:01:02 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:01:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:01:36 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:01:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:01:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:01:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:01:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:01:44 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.8秒)
2025-08-18 09:01:44 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:01:44 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:01:44 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:01:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:01:53 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:01:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:01:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:01:53 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:01:53 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:02:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:02:26 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:02:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:02:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:02:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:02:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:02:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.1秒)
2025-08-18 09:02:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:02:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:02:33 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:02:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:02:40 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:02:40 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:02:40 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:02:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:02:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:03:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:03:16 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:03:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:03:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:03:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:03:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:03:24 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.9秒)
2025-08-18 09:03:24 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:03:24 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:03:24 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:03:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:03:32 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:03:32 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:03:32 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:03:32 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:03:32 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:04:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:04:06 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:04:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:04:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:04:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:04:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:04:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.3秒)
2025-08-18 09:04:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:04:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:04:13 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:04:13 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:04:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:04:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:04:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:04:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:04:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:04:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:04:56 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:04:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:05:04 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:05:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:05:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:05:04 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.8秒)
2025-08-18 09:05:04 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:05:04 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:05:04 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:05:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:05:11 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:05:11 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:05:11 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:05:11 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:05:11 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:05:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:05:46 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:05:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:05:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:05:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:05:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:05:55 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.6秒)
2025-08-18 09:05:55 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:05:55 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:05:55 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 56个
2025-08-18 09:05:55 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:05:55 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:06:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:06:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:06:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:06:03 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:06:03 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:06:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:06:36 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:06:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:06:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:06:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:06:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:06:44 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.7秒)
2025-08-18 09:06:44 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:06:44 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:06:44 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:06:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:06:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:06:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:06:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:06:52 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:06:52 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:07:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:07:26 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:07:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:07:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:07:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:07:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:07:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.7秒)
2025-08-18 09:07:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:07:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:07:33 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:07:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:07:41 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:07:41 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:07:41 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:07:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:07:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:08:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:08:16 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:08:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:08:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:08:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:08:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:08:23 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 09:08:23 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:08:23 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:08:24 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:08:24 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:08:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:08:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:08:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:08:30 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:08:30 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:09:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:09:06 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:09:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:09:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:09:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:09:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:09:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.4秒)
2025-08-18 09:09:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:09:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:09:14 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:09:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:09:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:09:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:09:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:09:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:09:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:09:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:09:56 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:09:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:10:02 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:10:02 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:10:02 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:10:02 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.5秒)
2025-08-18 09:10:02 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:10:02 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:10:03 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:10:03 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:10:09 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:10:09 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:10:09 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:10:09 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:10:09 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:10:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:10:46 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:10:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:10:53 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:10:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:10:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:10:53 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 09:10:53 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:10:53 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:10:54 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:10:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:11:01 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:11:01 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:11:01 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:11:01 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:11:01 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:11:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:11:36 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:11:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:11:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:11:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:11:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:11:44 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.8秒)
2025-08-18 09:11:44 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:11:44 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:11:44 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:11:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:11:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:11:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:11:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:11:51 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:11:51 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:12:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:12:26 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:12:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:12:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:12:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:12:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:12:34 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.2秒)
2025-08-18 09:12:34 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:12:34 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:12:34 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:12:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:12:42 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:12:42 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:12:42 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:12:42 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:12:42 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:13:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:13:16 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:13:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:13:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:13:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:13:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:13:23 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 09:13:23 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:13:23 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:13:23 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:13:23 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:13:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:13:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:13:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:13:30 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:13:30 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:14:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:14:06 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:14:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:14:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:14:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:14:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:14:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.9秒)
2025-08-18 09:14:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:14:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:14:13 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 56个
2025-08-18 09:14:13 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:14:13 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:14:20 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:14:20 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:14:20 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:14:20 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:14:20 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:14:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:14:56 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:14:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:15:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:15:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:15:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:15:03 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.9秒)
2025-08-18 09:15:03 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:15:03 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:15:03 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:15:03 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:15:09 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:15:09 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:15:09 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:15:09 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:15:09 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:15:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:15:46 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:15:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:15:53 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:15:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:15:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:15:53 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.9秒)
2025-08-18 09:15:53 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:15:53 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:15:53 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:15:53 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:16:01 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:16:01 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:16:01 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:16:01 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:16:01 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:16:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:16:36 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:16:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:16:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:16:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:16:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:16:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 09:16:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:16:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:16:43 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:16:43 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:16:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:16:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:16:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:16:51 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:16:51 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:17:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:17:26 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:17:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:17:35 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:17:35 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:17:35 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:17:35 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.8秒)
2025-08-18 09:17:35 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:17:35 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 56
2025-08-18 09:17:35 - src.email_handler.simple_email_client - DEBUG - 找到 56 封邮件
2025-08-18 09:17:35 - src.email_handler.simple_email_client - INFO - 邮件数量过多(56)，只获取最新的50封
2025-08-18 09:17:42 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:17:42 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:17:42 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:17:43 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:17:43 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:18:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:18:16 - src.email_handler.simple_email_client - DEBUG - 找到 58 封邮件
2025-08-18 09:18:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(58)，只获取最新的50封
2025-08-18 09:18:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:18:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:18:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:18:24 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 09:17:41
2025-08-18 09:18:24 - src.core.universal_email_pusher - DEBUG - ⏭️ 跳过: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 09:17:41 (不匹配关键字)
2025-08-18 09:18:24 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 09:17:27
2025-08-18 09:18:24 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 09:17:27
2025-08-18 09:18:24 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 09:17:27 (长度: 3004)
2025-08-18 09:18:24 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 09:17:27
2025-08-18 09:18:24 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 09:17:27
2025-08-18 09:18:25 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_091727_p1.png (276229 bytes)
2025-08-18 09:18:25 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 09:18:25 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 09:18:26 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 09:18:26 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_091727_p1.png
2025-08-18 09:18:26 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 09:17:27, 成功推送 1 张图片
2025-08-18 09:18:26 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 09:17:27
2025-08-18 09:18:26 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 09:17:27
2025-08-18 09:18:26 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 10.5秒)
2025-08-18 09:18:26 - src.core.universal_email_pusher - INFO - 📊 新邮件: 2封, 推送: 1封
2025-08-18 09:18:26 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 58
2025-08-18 09:18:27 - src.email_handler.simple_email_client - DEBUG - 找到 58 封邮件
2025-08-18 09:18:27 - src.email_handler.simple_email_client - INFO - 邮件数量过多(58)，只获取最新的50封
2025-08-18 09:18:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:18:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:18:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:18:34 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:18:34 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:19:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:19:06 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:19:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:19:14 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:19:14 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:19:14 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:19:14 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 09:18:42
2025-08-18 09:19:14 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 09:18:42
2025-08-18 09:19:15 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 09:18:42 (长度: 2663)
2025-08-18 09:19:15 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 09:18:42
2025-08-18 09:19:15 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 09:18:42
2025-08-18 09:19:15 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_091842_p1.png (244317 bytes)
2025-08-18 09:19:15 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 09:19:15 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 09:19:17 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 09:19:17 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_091842_p1.png
2025-08-18 09:19:17 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 09:18:42, 成功推送 1 张图片
2025-08-18 09:19:17 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 09:18:42
2025-08-18 09:19:17 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 09:18:42
2025-08-18 09:19:17 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 10.8秒)
2025-08-18 09:19:17 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 09:19:17 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:19:17 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:19:17 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:19:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:19:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:19:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:19:24 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:19:24 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:19:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:19:56 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:19:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:20:04 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:20:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:20:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:20:04 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.2秒)
2025-08-18 09:20:04 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:20:04 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:20:04 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:20:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:20:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:20:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:20:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:20:13 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:20:13 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:20:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:20:46 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:20:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:20:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:20:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:20:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:20:55 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.9秒)
2025-08-18 09:20:55 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:20:55 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:20:55 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:20:55 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:21:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:21:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:21:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:21:03 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:21:03 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:21:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:21:36 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:21:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:21:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:21:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:21:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:21:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 09:21:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:21:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:21:44 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:21:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:21:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:21:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:21:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:21:52 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:21:52 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:22:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:22:26 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:22:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:22:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:22:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:22:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:22:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 09:22:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:22:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:22:33 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 59个
2025-08-18 09:22:33 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:22:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:22:41 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:22:41 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:22:41 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:22:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:22:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:23:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:23:16 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:23:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:23:24 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:23:24 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:23:24 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:23:24 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.4秒)
2025-08-18 09:23:24 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:23:24 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:23:25 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:23:25 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:23:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:23:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:23:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:23:33 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:23:33 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:24:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:24:06 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:24:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:24:14 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:24:14 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:24:14 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:24:14 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.9秒)
2025-08-18 09:24:14 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:24:14 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:24:14 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:24:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:24:22 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:24:22 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:24:22 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:24:22 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:24:22 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:24:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:24:56 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:24:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:25:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:25:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:25:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:25:03 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 6.7秒)
2025-08-18 09:25:03 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:25:03 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:25:03 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:25:03 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:25:09 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:25:09 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:25:09 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:25:09 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:25:09 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:25:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:25:46 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:25:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:25:53 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:25:53 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:25:53 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:25:53 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.3秒)
2025-08-18 09:25:53 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:25:53 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:25:53 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:25:53 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:26:01 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:26:01 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:26:01 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:26:01 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:26:01 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:26:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:26:36 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:26:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:26:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:26:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:26:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:26:43 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 09:26:43 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:26:43 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:26:43 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:26:43 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:26:50 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:26:50 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:26:50 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:26:50 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:26:50 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:27:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:27:26 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:27:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:27:33 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:27:33 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:27:33 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:27:33 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 09:27:33 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:27:33 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:27:33 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:27:33 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:27:41 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:27:41 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:27:41 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:27:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:27:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:28:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:28:16 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:28:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:28:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:28:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:28:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:28:23 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.0秒)
2025-08-18 09:28:23 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:28:23 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:28:23 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:28:23 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:28:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:28:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:28:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:28:30 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:28:30 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:29:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:29:06 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:29:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:29:14 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:29:14 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:29:14 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:29:14 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 09:29:14 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:29:14 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:29:14 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:29:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:29:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:29:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:29:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:29:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:29:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:29:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:29:56 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:29:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:30:04 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:30:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:30:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:30:04 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.6秒)
2025-08-18 09:30:04 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:30:04 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:30:04 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:30:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:30:11 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:30:11 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:30:11 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:30:11 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:30:11 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:30:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:30:46 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:30:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:30:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:30:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:30:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:30:54 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.7秒)
2025-08-18 09:30:54 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:30:54 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:30:54 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 59个
2025-08-18 09:30:54 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:30:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:31:00 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:31:00 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:31:00 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:31:00 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:31:00 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:31:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:31:36 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:31:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:31:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:31:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:31:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:31:44 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 09:31:44 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:31:44 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:31:44 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:31:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:31:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:31:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:31:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:31:51 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:31:51 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:32:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:32:26 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:32:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:32:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:32:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:32:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:32:34 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.1秒)
2025-08-18 09:32:34 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:32:34 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:32:34 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:32:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:32:42 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:32:42 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:32:42 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:32:42 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:32:42 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:33:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:33:16 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:33:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:33:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:33:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:33:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:33:23 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.1秒)
2025-08-18 09:33:23 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:33:23 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:33:23 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:33:23 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:33:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:33:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:33:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:33:30 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:33:30 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:34:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:34:06 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:34:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:34:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:34:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:34:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:34:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.4秒)
2025-08-18 09:34:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:34:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:34:14 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:34:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:34:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:34:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:34:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:34:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:34:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:34:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:34:56 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:34:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:35:04 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:35:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:35:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:35:04 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.6秒)
2025-08-18 09:35:04 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:35:04 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:35:04 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:35:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:35:11 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:35:11 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:35:11 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:35:11 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:35:11 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:35:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:35:46 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:35:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:35:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:35:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:35:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:35:55 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.6秒)
2025-08-18 09:35:55 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:35:55 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:35:55 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:35:55 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:36:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:36:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:36:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:36:03 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:36:03 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:36:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:36:36 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:36:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:36:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:36:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:36:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:36:44 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.9秒)
2025-08-18 09:36:44 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:36:44 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:36:44 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:36:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:36:52 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:36:52 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:36:52 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:36:52 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:36:52 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:37:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:37:26 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:37:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:37:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:37:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:37:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:37:34 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.7秒)
2025-08-18 09:37:34 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:37:34 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:37:34 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:37:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:37:41 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:37:41 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:37:41 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:37:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:37:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:38:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:38:16 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:38:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:38:23 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:38:23 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:38:23 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:38:23 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.1秒)
2025-08-18 09:38:23 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:38:23 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:38:23 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:38:23 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:38:31 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:38:31 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:38:31 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:38:31 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:38:31 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:39:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:39:06 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:39:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:39:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:39:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:39:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:39:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.1秒)
2025-08-18 09:39:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:39:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:39:13 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 59个
2025-08-18 09:39:13 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:39:13 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:39:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:39:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:39:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:39:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:39:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:39:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:39:56 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:39:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:40:04 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:40:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:40:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:40:04 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 09:40:04 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:40:04 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:40:04 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:40:04 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:40:11 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:40:11 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:40:11 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:40:11 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:40:11 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:40:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:40:46 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:40:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:40:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:40:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:40:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:40:55 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 9.3秒)
2025-08-18 09:40:55 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:40:55 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 59
2025-08-18 09:40:56 - src.email_handler.simple_email_client - DEBUG - 找到 59 封邮件
2025-08-18 09:40:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(59)，只获取最新的50封
2025-08-18 09:41:04 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:41:04 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:41:04 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:41:04 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:41:04 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:41:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:41:36 - src.email_handler.simple_email_client - DEBUG - 找到 61 封邮件
2025-08-18 09:41:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(61)，只获取最新的50封
2025-08-18 09:41:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:41:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:41:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:41:43 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 09:41:17
2025-08-18 09:41:43 - src.core.universal_email_pusher - DEBUG - ⏭️ 跳过: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 09:41:17 (不匹配关键字)
2025-08-18 09:41:43 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 09:40:52
2025-08-18 09:41:43 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 09:40:52
2025-08-18 09:41:44 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 09:40:52 (长度: 3590)
2025-08-18 09:41:44 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 09:40:52
2025-08-18 09:41:44 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 09:40:52
2025-08-18 09:41:45 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_094052_p1.png (345167 bytes)
2025-08-18 09:41:45 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 09:41:45 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 09:41:46 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 09:41:46 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_094052_p1.png
2025-08-18 09:41:46 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 09:40:52, 成功推送 1 张图片
2025-08-18 09:41:46 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 09:40:52
2025-08-18 09:41:46 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 09:40:52
2025-08-18 09:41:46 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 10.1秒)
2025-08-18 09:41:46 - src.core.universal_email_pusher - INFO - 📊 新邮件: 2封, 推送: 1封
2025-08-18 09:41:46 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 61
2025-08-18 09:41:46 - src.email_handler.simple_email_client - DEBUG - 找到 61 封邮件
2025-08-18 09:41:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(61)，只获取最新的50封
2025-08-18 09:41:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:41:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:41:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:41:54 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:41:54 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:42:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:42:26 - src.email_handler.simple_email_client - DEBUG - 找到 62 封邮件
2025-08-18 09:42:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(62)，只获取最新的50封
2025-08-18 09:42:34 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:42:34 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:42:34 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:42:34 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 09:41:50
2025-08-18 09:42:34 - src.core.universal_email_pusher - DEBUG - ⏭️ 跳过: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 09:41:50 (不匹配关键字)
2025-08-18 09:42:34 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.6秒)
2025-08-18 09:42:34 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 0封
2025-08-18 09:42:34 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 62
2025-08-18 09:42:34 - src.email_handler.simple_email_client - DEBUG - 找到 62 封邮件
2025-08-18 09:42:34 - src.email_handler.simple_email_client - INFO - 邮件数量过多(62)，只获取最新的50封
2025-08-18 09:42:41 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:42:41 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:42:41 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:42:41 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:42:41 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:43:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:43:16 - src.email_handler.simple_email_client - DEBUG - 找到 63 封邮件
2025-08-18 09:43:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(63)，只获取最新的50封
2025-08-18 09:43:25 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:43:25 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:43:25 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:43:25 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 09:42:58
2025-08-18 09:43:25 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 09:42:58
2025-08-18 09:43:26 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 09:42:58 (长度: 3590)
2025-08-18 09:43:26 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 09:42:58
2025-08-18 09:43:26 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 09:42:58
2025-08-18 09:43:26 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_094258_p1.png (345079 bytes)
2025-08-18 09:43:26 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 09:43:26 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 09:43:28 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 09:43:28 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_094258_p1.png
2025-08-18 09:43:28 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 09:42:58, 成功推送 1 张图片
2025-08-18 09:43:28 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 09:42:58
2025-08-18 09:43:28 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 09:42:58
2025-08-18 09:43:28 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 11.7秒)
2025-08-18 09:43:28 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 09:43:28 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 63
2025-08-18 09:43:28 - src.email_handler.simple_email_client - DEBUG - 找到 63 封邮件
2025-08-18 09:43:28 - src.email_handler.simple_email_client - INFO - 邮件数量过多(63)，只获取最新的50封
2025-08-18 09:43:35 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:43:35 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:43:35 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:43:35 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:43:35 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:44:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:44:06 - src.email_handler.simple_email_client - DEBUG - 找到 63 封邮件
2025-08-18 09:44:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(63)，只获取最新的50封
2025-08-18 09:44:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:44:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:44:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:44:13 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.2秒)
2025-08-18 09:44:13 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:44:13 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 63
2025-08-18 09:44:14 - src.email_handler.simple_email_client - DEBUG - 找到 63 封邮件
2025-08-18 09:44:14 - src.email_handler.simple_email_client - INFO - 邮件数量过多(63)，只获取最新的50封
2025-08-18 09:44:21 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:44:21 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:44:21 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:44:21 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:44:21 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:44:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:44:56 - src.email_handler.simple_email_client - DEBUG - 找到 64 封邮件
2025-08-18 09:44:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(64)，只获取最新的50封
2025-08-18 09:45:03 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:45:03 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:45:03 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:45:03 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 09:44:22
2025-08-18 09:45:03 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 09:44:22
2025-08-18 09:45:04 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 09:44:22 (长度: 2663)
2025-08-18 09:45:04 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 09:44:22
2025-08-18 09:45:04 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 09:44:22
2025-08-18 09:45:04 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_094422_p1.png (244405 bytes)
2025-08-18 09:45:04 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 09:45:04 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 09:45:05 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 09:45:05 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_094422_p1.png
2025-08-18 09:45:05 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 09:44:22, 成功推送 1 张图片
2025-08-18 09:45:05 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 09:44:22
2025-08-18 09:45:05 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 09:44:22
2025-08-18 09:45:05 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 9.3秒)
2025-08-18 09:45:05 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 09:45:05 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 64
2025-08-18 09:45:05 - src.email_handler.simple_email_client - DEBUG - 找到 64 封邮件
2025-08-18 09:45:05 - src.email_handler.simple_email_client - INFO - 邮件数量过多(64)，只获取最新的50封
2025-08-18 09:45:13 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:45:13 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:45:13 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:45:13 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:45:13 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:45:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:45:46 - src.email_handler.simple_email_client - DEBUG - 找到 64 封邮件
2025-08-18 09:45:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(64)，只获取最新的50封
2025-08-18 09:45:54 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:45:54 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:45:54 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:45:54 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.5秒)
2025-08-18 09:45:54 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:45:54 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 64
2025-08-18 09:45:54 - src.email_handler.simple_email_client - DEBUG - 找到 64 封邮件
2025-08-18 09:45:54 - src.email_handler.simple_email_client - INFO - 邮件数量过多(64)，只获取最新的50封
2025-08-18 09:46:01 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:46:01 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:46:01 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:46:01 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:46:01 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:46:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:46:36 - src.email_handler.simple_email_client - DEBUG - 找到 64 封邮件
2025-08-18 09:46:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(64)，只获取最新的50封
2025-08-18 09:46:44 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:46:44 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:46:44 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:46:44 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 7.6秒)
2025-08-18 09:46:44 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:46:44 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 64
2025-08-18 09:46:44 - src.email_handler.simple_email_client - DEBUG - 找到 64 封邮件
2025-08-18 09:46:44 - src.email_handler.simple_email_client - INFO - 邮件数量过多(64)，只获取最新的50封
2025-08-18 09:46:51 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:46:51 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:46:51 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:46:51 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:46:51 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:47:26 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:47:26 - src.email_handler.simple_email_client - DEBUG - 找到 65 封邮件
2025-08-18 09:47:26 - src.email_handler.simple_email_client - INFO - 邮件数量过多(65)，只获取最新的50封
2025-08-18 09:47:35 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:47:35 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:47:35 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:47:35 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 09:46:59
2025-08-18 09:47:35 - src.core.universal_email_pusher - DEBUG - ⏭️ 跳过: [测试邮件] 告警监控系统邮件测试 - 2025-08-18 09:46:59 (不匹配关键字)
2025-08-18 09:47:35 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.8秒)
2025-08-18 09:47:35 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 0封
2025-08-18 09:47:35 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 65
2025-08-18 09:47:35 - src.core.universal_email_pusher - INFO - 💾 状态加载成功，有效UID: 65个
2025-08-18 09:47:35 - src.email_handler.simple_email_client - DEBUG - 找到 65 封邮件
2025-08-18 09:47:35 - src.email_handler.simple_email_client - INFO - 邮件数量过多(65)，只获取最新的50封
2025-08-18 09:47:43 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:47:43 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:47:43 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:47:43 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:47:43 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:48:16 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:48:16 - src.email_handler.simple_email_client - DEBUG - 找到 65 封邮件
2025-08-18 09:48:16 - src.email_handler.simple_email_client - INFO - 邮件数量过多(65)，只获取最新的50封
2025-08-18 09:48:25 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:48:25 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:48:25 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:48:25 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.8秒)
2025-08-18 09:48:25 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:48:25 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 65
2025-08-18 09:48:25 - src.email_handler.simple_email_client - DEBUG - 找到 65 封邮件
2025-08-18 09:48:25 - src.email_handler.simple_email_client - INFO - 邮件数量过多(65)，只获取最新的50封
2025-08-18 09:48:36 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:48:36 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:48:36 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:48:36 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:48:36 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:49:06 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:49:06 - src.email_handler.simple_email_client - DEBUG - 找到 66 封邮件
2025-08-18 09:49:06 - src.email_handler.simple_email_client - INFO - 邮件数量过多(66)，只获取最新的50封
2025-08-18 09:49:18 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:49:18 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:49:18 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:49:18 - src.core.universal_email_pusher - DEBUG - 🆕 发现新邮件: 汇总-0818 09:48:15
2025-08-18 09:49:18 - src.core.universal_email_pusher - DEBUG - 邮件匹配关键字 '汇总': 汇总-0818 09:48:15
2025-08-18 09:49:19 - src.core.universal_email_pusher - DEBUG - 成功获取邮件完整内容: 汇总-0818 09:48:15 (长度: 11836)
2025-08-18 09:49:19 - src.core.universal_email_pusher - INFO - 📷 仅图片模式推送: 汇总-0818 09:48:15
2025-08-18 09:49:19 - src.core.universal_email_pusher - INFO - 📷 开始检查邮件图片附件: 汇总-0818 09:48:15
2025-08-18 09:49:19 - src.email_handler.attachment_handler - INFO - 提取图片附件: AlarmReport_20250818_094815_p1.png (345026 bytes)
2025-08-18 09:49:19 - src.email_handler.attachment_handler - INFO - 共提取到 1 个图片附件
2025-08-18 09:49:19 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): qyapi.weixin.qq.com:443
2025-08-18 09:49:21 - urllib3.connectionpool - DEBUG - https://qyapi.weixin.qq.com:443 "POST /cgi-bin/webhook/send?key=fd1f28c8-91b6-4710-940b-994bfa3bf744 HTTP/11" 200 27
2025-08-18 09:49:21 - src.pusher.dingtalk_pusher - INFO - 📷 图片推送成功: AlarmReport_20250818_094815_p1.png
2025-08-18 09:49:21 - src.core.universal_email_pusher - INFO - 📷 图片附件推送成功: 汇总-0818 09:48:15, 成功推送 1 张图片
2025-08-18 09:49:21 - src.core.universal_email_pusher - INFO - 📤 仅图片推送成功: 汇总-0818 09:48:15
2025-08-18 09:49:21 - src.core.universal_email_pusher - INFO - 📤 推送: 汇总-0818 09:48:15
2025-08-18 09:49:21 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 14.7秒)
2025-08-18 09:49:21 - src.core.universal_email_pusher - INFO - 📊 新邮件: 1封, 推送: 1封
2025-08-18 09:49:21 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 66
2025-08-18 09:49:21 - src.email_handler.simple_email_client - DEBUG - 找到 66 封邮件
2025-08-18 09:49:21 - src.email_handler.simple_email_client - INFO - 邮件数量过多(66)，只获取最新的50封
2025-08-18 09:49:30 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:49:30 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:49:30 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:49:30 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:49:30 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:49:56 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:49:56 - src.email_handler.simple_email_client - DEBUG - 找到 66 封邮件
2025-08-18 09:49:56 - src.email_handler.simple_email_client - INFO - 邮件数量过多(66)，只获取最新的50封
2025-08-18 09:50:05 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:50:05 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:50:05 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:50:05 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 8.9秒)
2025-08-18 09:50:05 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:50:05 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 66
2025-08-18 09:50:05 - src.email_handler.simple_email_client - DEBUG - 找到 66 封邮件
2025-08-18 09:50:05 - src.email_handler.simple_email_client - INFO - 邮件数量过多(66)，只获取最新的50封
2025-08-18 09:50:14 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:50:14 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:50:14 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:50:14 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:50:14 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:50:46 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:50:46 - src.email_handler.simple_email_client - DEBUG - 找到 66 封邮件
2025-08-18 09:50:46 - src.email_handler.simple_email_client - INFO - 邮件数量过多(66)，只获取最新的50封
2025-08-18 09:50:55 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:50:55 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:50:55 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:50:55 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 9.3秒)
2025-08-18 09:50:55 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:50:55 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 66
2025-08-18 09:50:55 - src.email_handler.simple_email_client - DEBUG - 找到 66 封邮件
2025-08-18 09:50:55 - src.email_handler.simple_email_client - INFO - 邮件数量过多(66)，只获取最新的50封
2025-08-18 09:51:05 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:51:05 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:51:05 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:51:06 - src.gui.email_list_widget - INFO - 邮件列表已更新，共 50 封邮件
2025-08-18 09:51:06 - src.gui.main_window - INFO - 📧 邮件列表已更新，共 50 封邮件
2025-08-18 09:51:36 - src.core.universal_email_pusher - INFO - 🔍 开始检查新邮件...
2025-08-18 09:51:36 - src.email_handler.simple_email_client - DEBUG - 找到 66 封邮件
2025-08-18 09:51:36 - src.email_handler.simple_email_client - INFO - 邮件数量过多(66)，只获取最新的50封
2025-08-18 09:51:49 - src.email_handler.simple_email_client - INFO - 成功获取 50 封邮件
2025-08-18 09:51:49 - src.core.universal_email_pusher - DEBUG - 快速获取邮件: <EMAIL> - 50封
2025-08-18 09:51:49 - src.core.universal_email_pusher - DEBUG - 📧 总共获取到 50 封邮件
2025-08-18 09:51:49 - src.core.universal_email_pusher - INFO - ✅ 检查完成 (耗时: 12.6秒)
2025-08-18 09:51:49 - src.core.universal_email_pusher - INFO - 📊 新邮件: 0封, 推送: 0封
2025-08-18 09:51:49 - src.core.universal_email_pusher - DEBUG - 💾 状态保存成功，UID数量: 66
2025-08-18 09:51:49 - src.email_handler.simple_email_client - DEBUG - 找到 66 封邮件
2025-08-18 09:51:49 - src.email_handler.simple_email_client - INFO - 邮件数量过多(66)，只获取最新的50封
