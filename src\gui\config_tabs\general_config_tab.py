# -*- coding: utf-8 -*-
"""
通用设置选项卡
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                               QFormLayout, QSpinBox, QCheckBox, QComboBox, 
                               QLabel, QMessageBox, QPushButton, QFileDialog,
                               QLineEdit)
from PySide6.QtCore import Qt
from src.config.config_manager import config_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class GeneralConfigTab(QWidget):
    """通用设置选项卡"""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 自动刷新设置
        self._create_auto_refresh_group(layout)
        
        # 日志设置
        self._create_log_group(layout)
        
        # 通知设置
        self._create_notification_group(layout)

        # 图片附件设置
        self._create_image_attachment_group(layout)

        # 高级设置
        self._create_advanced_group(layout)
        
        layout.addStretch()
    
    def _create_auto_refresh_group(self, parent_layout):
        """创建自动刷新设置组"""
        group = QGroupBox("自动刷新设置")
        layout = QFormLayout(group)
        
        # 启用自动刷新
        self.auto_refresh_check = QCheckBox("启用自动刷新")
        self.auto_refresh_check.setToolTip("启用后将定时检查新邮件")
        layout.addRow("", self.auto_refresh_check)
        
        # 刷新间隔
        self.refresh_interval_spin = QSpinBox()
        self.refresh_interval_spin.setRange(10, 3600)  # 10秒到1小时
        self.refresh_interval_spin.setValue(60)
        self.refresh_interval_spin.setSuffix(" 秒")
        self.refresh_interval_spin.setToolTip("自动刷新的时间间隔")
        layout.addRow("刷新间隔:", self.refresh_interval_spin)
        
        # 启动时自动刷新
        self.startup_refresh_check = QCheckBox("启动时自动刷新")
        self.startup_refresh_check.setChecked(True)
        self.startup_refresh_check.setToolTip("程序启动时自动执行一次邮件刷新")
        layout.addRow("", self.startup_refresh_check)
        
        parent_layout.addWidget(group)
    
    def _create_log_group(self, parent_layout):
        """创建日志设置组"""
        group = QGroupBox("日志设置")
        layout = QFormLayout(group)
        
        # 日志级别
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        self.log_level_combo.setToolTip("设置日志记录级别")
        layout.addRow("日志级别:", self.log_level_combo)
        
        # 日志文件大小限制
        self.log_size_spin = QSpinBox()
        self.log_size_spin.setRange(1, 100)
        self.log_size_spin.setValue(10)
        self.log_size_spin.setSuffix(" MB")
        self.log_size_spin.setToolTip("单个日志文件的最大大小")
        layout.addRow("文件大小限制:", self.log_size_spin)
        
        # 日志文件数量
        self.log_count_spin = QSpinBox()
        self.log_count_spin.setRange(1, 20)
        self.log_count_spin.setValue(5)
        self.log_count_spin.setToolTip("保留的日志文件数量")
        layout.addRow("保留文件数:", self.log_count_spin)
        
        # 打开日志目录按钮
        self.open_log_button = QPushButton("📁 打开日志目录")
        layout.addRow("", self.open_log_button)
        
        parent_layout.addWidget(group)
    
    def _create_notification_group(self, parent_layout):
        """创建通知设置组"""
        group = QGroupBox("通知设置")
        layout = QFormLayout(group)
        
        # 桌面通知
        self.desktop_notification_check = QCheckBox("启用桌面通知")
        self.desktop_notification_check.setToolTip("收到新邮件时显示桌面通知")
        layout.addRow("", self.desktop_notification_check)
        
        # 声音提醒
        self.sound_notification_check = QCheckBox("启用声音提醒")
        self.sound_notification_check.setToolTip("收到新邮件时播放提示音")
        layout.addRow("", self.sound_notification_check)
        
        # 推送成功通知
        self.push_success_notification_check = QCheckBox("推送成功时通知")
        self.push_success_notification_check.setChecked(True)
        self.push_success_notification_check.setToolTip("邮件推送成功时显示通知")
        layout.addRow("", self.push_success_notification_check)
        
        parent_layout.addWidget(group)

    def _create_image_attachment_group(self, parent_layout):
        """创建图片附件设置组"""
        group = QGroupBox("图片附件推送")
        layout = QFormLayout(group)

        # 启用图片附件推送
        self.image_attachment_check = QCheckBox("推送邮件中的图片附件")
        self.image_attachment_check.setToolTip("启用后会自动推送邮件中的图片附件到企业微信")
        layout.addRow("", self.image_attachment_check)

        # 最大文件大小
        self.max_size_spin = QSpinBox()
        self.max_size_spin.setRange(1, 20)
        self.max_size_spin.setValue(2)
        self.max_size_spin.setSuffix(" MB")
        self.max_size_spin.setToolTip("单个图片文件的最大大小限制")
        layout.addRow("最大文件大小:", self.max_size_spin)

        # 最大图片数量
        self.max_count_spin = QSpinBox()
        self.max_count_spin.setRange(1, 10)
        self.max_count_spin.setValue(3)
        self.max_count_spin.setToolTip("每封邮件最多推送的图片数量")
        layout.addRow("最大图片数量:", self.max_count_spin)

        # 推送方式
        self.push_as_file_check = QCheckBox("作为文件推送（支持更大文件）")
        self.push_as_file_check.setToolTip("勾选：作为文件推送（最大20MB）\n不勾选：作为图片推送（最大2MB，直接显示）")
        layout.addRow("", self.push_as_file_check)

        # 仅推送图片选项
        self.image_only_check = QCheckBox("仅推送图片（不推送邮件正文）")
        self.image_only_check.setToolTip("勾选：只推送图片附件，不推送邮件正文\n不勾选：推送邮件正文 + 图片附件")
        layout.addRow("", self.image_only_check)

        # 支持的格式（只读显示）
        format_label = QLabel("jpg, jpeg, png, gif, bmp")
        format_label.setStyleSheet("color: gray; font-size: 11px;")
        layout.addRow("支持格式:", format_label)

        parent_layout.addWidget(group)

    def _create_advanced_group(self, parent_layout):
        """创建高级设置组"""
        group = QGroupBox("高级设置")
        layout = QFormLayout(group)
        
        # 邮件获取数量限制
        self.email_limit_spin = QSpinBox()
        self.email_limit_spin.setRange(10, 1000)
        self.email_limit_spin.setValue(50)
        self.email_limit_spin.setToolTip("每次刷新获取的邮件数量上限")
        layout.addRow("邮件获取限制:", self.email_limit_spin)
        
        # 连接超时时间
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 120)
        self.timeout_spin.setValue(30)
        self.timeout_spin.setSuffix(" 秒")
        self.timeout_spin.setToolTip("网络连接超时时间")
        layout.addRow("连接超时:", self.timeout_spin)
        
        # 重试次数
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(0, 10)
        self.retry_spin.setValue(3)
        self.retry_spin.setToolTip("连接失败时的重试次数")
        layout.addRow("重试次数:", self.retry_spin)
        
        # 最小化到系统托盘
        self.minimize_to_tray_check = QCheckBox("最小化到系统托盘")
        self.minimize_to_tray_check.setToolTip("关闭窗口时最小化到系统托盘而不是退出程序")
        layout.addRow("", self.minimize_to_tray_check)
        
        # 开机自启动
        self.auto_start_check = QCheckBox("开机自启动")
        self.auto_start_check.setToolTip("系统启动时自动运行程序")
        layout.addRow("", self.auto_start_check)
        
        parent_layout.addWidget(group)
    
    def _connect_signals(self):
        """连接信号"""
        self.auto_refresh_check.toggled.connect(self.refresh_interval_spin.setEnabled)
        self.open_log_button.clicked.connect(self.open_log_directory)

        # 图片附件相关信号
        self.image_attachment_check.toggled.connect(self._on_image_attachment_toggled)

    def _on_image_attachment_toggled(self, enabled):
        """图片附件开关切换"""
        self.max_size_spin.setEnabled(enabled)
        self.max_count_spin.setEnabled(enabled)
        self.push_as_file_check.setEnabled(enabled)
        self.image_only_check.setEnabled(enabled)
    
    def load_config(self):
        """加载配置"""
        # 加载自动刷新配置
        auto_refresh_config = config_manager.get_auto_refresh_config()
        self.auto_refresh_check.setChecked(auto_refresh_config.get("enabled", True))
        self.refresh_interval_spin.setValue(auto_refresh_config.get("interval", 60))
        self.refresh_interval_spin.setEnabled(auto_refresh_config.get("enabled", True))

        # 加载图片附件配置
        image_config = config_manager.get_image_attachment_config()
        self.image_attachment_check.setChecked(image_config.get("enabled", False))
        self.max_size_spin.setValue(image_config.get("max_size_mb", 2))
        self.max_count_spin.setValue(image_config.get("max_count", 3))
        self.push_as_file_check.setChecked(image_config.get("push_as_file", False))
        self.image_only_check.setChecked(image_config.get("image_only", False))

        # 设置控件启用状态
        enabled = image_config.get("enabled", False)
        self.max_size_spin.setEnabled(enabled)
        self.max_count_spin.setEnabled(enabled)
        self.push_as_file_check.setEnabled(enabled)
        self.image_only_check.setEnabled(enabled)

        # 加载其他配置（如果配置管理器支持的话）
        # 这里可以扩展配置管理器来支持更多设置
    
    def save_config(self):
        """保存配置"""
        # 保存自动刷新配置
        config_manager.set_auto_refresh_config(
            self.auto_refresh_check.isChecked(),
            self.refresh_interval_spin.value()
        )

        # 保存图片附件配置
        image_config = {
            "enabled": self.image_attachment_check.isChecked(),
            "max_size_mb": self.max_size_spin.value(),
            "max_count": self.max_count_spin.value(),
            "push_as_file": self.push_as_file_check.isChecked(),
            "image_only": self.image_only_check.isChecked(),
            "supported_formats": ["jpg", "jpeg", "png", "gif", "bmp"]
        }
        config_manager.set_image_attachment_config(image_config)

        # 保存其他配置（如果需要的话）
        # 这里可以扩展配置管理器来保存更多设置
    
    def validate(self) -> bool:
        """验证配置"""
        # 验证刷新间隔
        if self.auto_refresh_check.isChecked():
            if self.refresh_interval_spin.value() < 10:
                QMessageBox.warning(self, "验证失败", "自动刷新间隔不能少于10秒")
                return False
        
        return True
    
    def open_log_directory(self):
        """打开日志目录"""
        import os
        import subprocess
        import platform
        
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        try:
            if platform.system() == "Windows":
                os.startfile(log_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", log_dir])
            else:  # Linux
                subprocess.run(["xdg-open", log_dir])
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开日志目录: {str(e)}")
    
    def reset_config(self):
        """重置配置"""
        # 重置自动刷新设置
        self.auto_refresh_check.setChecked(True)
        self.refresh_interval_spin.setValue(60)
        self.startup_refresh_check.setChecked(True)
        
        # 重置日志设置
        self.log_level_combo.setCurrentText("INFO")
        self.log_size_spin.setValue(10)
        self.log_count_spin.setValue(5)
        
        # 重置通知设置
        self.desktop_notification_check.setChecked(False)
        self.sound_notification_check.setChecked(False)
        self.push_success_notification_check.setChecked(True)
        
        # 重置高级设置
        self.email_limit_spin.setValue(50)
        self.timeout_spin.setValue(30)
        self.retry_spin.setValue(3)
        self.minimize_to_tray_check.setChecked(False)
        self.auto_start_check.setChecked(False)
