#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将邮件告警推送程序打包成exe
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def clean_build():
    """清理之前的构建文件"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理: {dir_name}")
    
    # 清理spec文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"已清理: {spec_file}")


def build_exe():
    """构建exe文件"""
    print("开始打包...")
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个exe文件
        '--windowed',                   # 不显示控制台窗口（GUI应用）
        '--name=邮件告警推送程序',        # 指定exe文件名
        '--icon=resources/icon.ico',    # 应用图标（如果有的话）
        '--add-data=config;config',     # 包含配置目录
        '--add-data=resources;resources', # 包含资源目录（如果有的话）
        '--hidden-import=PySide6.QtCore',
        '--hidden-import=PySide6.QtWidgets',
        '--hidden-import=PySide6.QtGui',
        '--hidden-import=requests',
        '--hidden-import=cryptography',
        '--clean',                      # 清理临时文件
        'main.py'                       # 主程序文件
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists('resources/icon.ico'):
        cmd = [arg for arg in cmd if not arg.startswith('--icon')]
    
    # 如果没有resources目录，移除相关参数
    if not os.path.exists('resources'):
        cmd = [arg for arg in cmd if not arg.startswith('--add-data=resources')]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("打包成功！")
        print(f"输出目录: {os.path.abspath('dist')}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def copy_config():
    """复制配置文件到输出目录"""
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("dist目录不存在，跳过配置文件复制")
        return
    
    # 复制配置目录
    if os.path.exists('config'):
        shutil.copytree('config', dist_dir / 'config', dirs_exist_ok=True)
        print("已复制配置文件到dist目录")
    
    # 复制README
    if os.path.exists('README.md'):
        shutil.copy2('README.md', dist_dir / 'README.md')
        print("已复制README到dist目录")


def create_installer_script():
    """创建安装脚本"""
    installer_content = '''@echo off
echo 邮件告警推送程序安装脚本
echo.

set "INSTALL_DIR=%ProgramFiles%\\AlertPusher"
set "DESKTOP_LINK=%USERPROFILE%\\Desktop\\邮件告警推送程序.lnk"

echo 正在安装到: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 复制程序文件...
copy "邮件告警推送程序.exe" "%INSTALL_DIR%\\"
if exist "config" xcopy "config" "%INSTALL_DIR%\\config\\" /E /I /Y

echo 创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_LINK%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\邮件告警推送程序.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo.
echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
pause
'''
    
    with open('dist/install.bat', 'w', encoding='gbk') as f:
        f.write(installer_content)
    print("已创建安装脚本: dist/install.bat")


def main():
    """主函数"""
    print("=== 邮件告警推送程序打包工具 ===")
    print()
    
    # 检查依赖
    try:
        import PyInstaller
    except ImportError:
        print("错误: 未安装PyInstaller")
        print("请运行: pip install pyinstaller")
        return False
    
    # 清理旧文件
    clean_build()
    
    # 构建exe
    if not build_exe():
        return False
    
    # 复制配置文件
    copy_config()
    
    # 创建安装脚本
    create_installer_script()
    
    print()
    print("=== 打包完成 ===")
    print("输出文件:")
    print(f"  - 主程序: dist/邮件告警推送程序.exe")
    print(f"  - 安装脚本: dist/install.bat")
    print()
    print("使用说明:")
    print("1. 将dist目录下的所有文件复制到目标电脑")
    print("2. 运行install.bat进行安装（可选）")
    print("3. 或直接运行邮件告警推送程序.exe")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
