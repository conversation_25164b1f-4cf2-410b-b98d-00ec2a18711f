#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件附件处理器 - 专门处理图片附件
"""

import email
import email.message
import base64
import hashlib
import os
from typing import List, Dict, Any, Optional, Tuple
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ImageAttachment:
    """图片附件类"""
    
    def __init__(self, filename: str, content: bytes, content_type: str):
        self.filename = filename
        self.content = content
        self.content_type = content_type
        self.size = len(content)
        
    def get_base64(self) -> str:
        """获取base64编码"""
        return base64.b64encode(self.content).decode('utf-8')
    
    def get_md5(self) -> str:
        """获取MD5值"""
        return hashlib.md5(self.content).hexdigest()
    
    def get_extension(self) -> str:
        """获取文件扩展名"""
        if '.' in self.filename:
            return self.filename.split('.')[-1].lower()
        return ''
    
    def is_supported_format(self, supported_formats: List[str]) -> bool:
        """检查是否是支持的格式"""
        ext = self.get_extension()
        return ext in [fmt.lower() for fmt in supported_formats]
    
    def is_size_valid(self, max_size_mb: float) -> bool:
        """检查文件大小是否有效"""
        max_size_bytes = max_size_mb * 1024 * 1024
        return self.size <= max_size_bytes


class AttachmentHandler:
    """附件处理器"""
    
    def __init__(self):
        pass
    
    def extract_image_attachments(self, email_message: email.message.Message, 
                                config: Dict[str, Any]) -> List[ImageAttachment]:
        """从邮件中提取图片附件
        
        Args:
            email_message: 邮件消息对象
            config: 图片附件配置
            
        Returns:
            图片附件列表
        """
        if not config.get("enabled", False):
            return []
        
        attachments = []
        max_count = config.get("max_count", 3)
        max_size_mb = config.get("max_size_mb", 2)
        supported_formats = config.get("supported_formats", ["jpg", "jpeg", "png"])
        
        try:
            # 遍历邮件的所有部分
            for part in email_message.walk():
                # 跳过非附件部分
                if part.get_content_maintype() == 'multipart':
                    continue
                
                # 获取附件信息
                content_disposition = part.get("Content-Disposition", "")
                content_type = part.get_content_type()
                filename = part.get_filename()
                
                # 检查是否是附件
                if not self._is_attachment(part, content_disposition, content_type, filename):
                    continue
                
                # 检查是否是图片
                if not self._is_image(content_type, filename):
                    continue
                
                # 获取附件内容
                content = part.get_payload(decode=True)
                if not content:
                    continue
                
                # 创建图片附件对象
                attachment = ImageAttachment(
                    filename=filename or f"image.{self._guess_extension(content_type)}",
                    content=content,
                    content_type=content_type
                )
                
                # 验证格式和大小
                if not attachment.is_supported_format(supported_formats):
                    logger.debug(f"跳过不支持的图片格式: {attachment.filename}")
                    continue
                
                if not attachment.is_size_valid(max_size_mb):
                    logger.debug(f"跳过过大的图片: {attachment.filename} ({attachment.size} bytes)")
                    continue
                
                attachments.append(attachment)
                logger.info(f"提取图片附件: {attachment.filename} ({attachment.size} bytes)")
                
                # 限制数量
                if len(attachments) >= max_count:
                    logger.info(f"已达到最大图片数量限制: {max_count}")
                    break
            
            logger.info(f"共提取到 {len(attachments)} 个图片附件")
            return attachments
            
        except Exception as e:
            logger.error(f"提取图片附件失败: {e}")
            return []
    
    def _is_attachment(self, part: email.message.Message, content_disposition: str, 
                      content_type: str, filename: str) -> bool:
        """判断是否是附件"""
        # 检查Content-Disposition
        if "attachment" in content_disposition.lower():
            return True
        
        # 检查是否有文件名
        if filename:
            return True
        
        # 检查是否是内联图片（有些邮件客户端会这样发送）
        if "inline" in content_disposition.lower() and self._is_image(content_type, filename):
            return True
        
        return False
    
    def _is_image(self, content_type: str, filename: str) -> bool:
        """判断是否是图片"""
        # 检查MIME类型
        if content_type and content_type.startswith('image/'):
            return True
        
        # 检查文件扩展名
        if filename:
            ext = filename.split('.')[-1].lower() if '.' in filename else ''
            image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg']
            return ext in image_extensions
        
        return False
    
    def _guess_extension(self, content_type: str) -> str:
        """根据MIME类型猜测文件扩展名"""
        mime_to_ext = {
            'image/jpeg': 'jpg',
            'image/png': 'png',
            'image/gif': 'gif',
            'image/bmp': 'bmp',
            'image/webp': 'webp',
            'image/tiff': 'tiff',
            'image/svg+xml': 'svg'
        }
        return mime_to_ext.get(content_type, 'jpg')
    
    def extract_from_email_uid(self, email_client, uid: str, config: Dict[str, Any]) -> List[ImageAttachment]:
        """根据邮件UID提取图片附件
        
        Args:
            email_client: 邮件客户端
            uid: 邮件UID
            config: 图片附件配置
            
        Returns:
            图片附件列表
        """
        try:
            if not email_client.connect():
                return []
            
            # 获取完整邮件
            status, msg_data = email_client.imap.uid('fetch', uid, "(RFC822)")
            if status != "OK" or not msg_data[0]:
                logger.debug(f"无法获取UID为{uid}的邮件")
                return []
            
            # 解析邮件
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # 提取图片附件
            return self.extract_image_attachments(email_message, config)
            
        except Exception as e:
            logger.error(f"根据UID提取图片附件失败: {e}")
            return []


# 全局附件处理器实例
attachment_handler = AttachmentHandler()
