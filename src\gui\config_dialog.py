# -*- coding: utf-8 -*-
"""
配置对话框
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, 
                               QWidget, QPushButton, QMessageBox)
from PySide6.QtCore import Signal
from src.gui.config_tabs.email_config_tab import EmailConfigTab
from src.gui.config_tabs.webhook_config_tab import WebhookConfigTab
from src.gui.config_tabs.keyword_config_tab import KeywordConfigTab
from src.gui.config_tabs.general_config_tab import GeneralConfigTab
from src.config.config_manager import config_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ConfigDialog(QDialog):
    """配置对话框"""
    
    # 信号定义
    config_saved = Signal()  # 配置保存完成
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(800, 600)
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 邮箱配置选项卡
        self.email_tab = EmailConfigTab()
        self.tab_widget.addTab(self.email_tab, "📧 邮箱配置")
        
        # Webhook配置选项卡
        self.webhook_tab = WebhookConfigTab()
        self.tab_widget.addTab(self.webhook_tab, "🔗 Webhook配置")
        
        # 关键字配置选项卡
        self.keyword_tab = KeywordConfigTab()
        self.tab_widget.addTab(self.keyword_tab, "🔍 关键字配置")
        
        # 通用设置选项卡
        self.general_tab = GeneralConfigTab()
        self.tab_widget.addTab(self.general_tab, "⚙️ 通用设置")
        
        # 创建按钮栏
        self._create_button_bar(layout)
    
    def _create_button_bar(self, parent_layout):
        """创建按钮栏"""
        button_layout = QHBoxLayout()
        parent_layout.addLayout(button_layout)
        
        button_layout.addStretch()
        
        # 测试连接按钮
        self.test_button = QPushButton("🔧 测试连接")
        self.test_button.clicked.connect(self.test_connections)
        button_layout.addWidget(self.test_button)
        
        # 重置按钮
        self.reset_button = QPushButton("🔄 重置")
        self.reset_button.clicked.connect(self.reset_config)
        button_layout.addWidget(self.reset_button)
        
        # 取消按钮
        self.cancel_button = QPushButton("❌ 取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        # 保存按钮
        self.save_button = QPushButton("💾 保存")
        self.save_button.clicked.connect(self.save_config)
        self.save_button.setDefault(True)
        button_layout.addWidget(self.save_button)
    
    def _connect_signals(self):
        """连接信号"""
        pass
    
    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        # 只在首次显示时加载配置
        if not hasattr(self, '_config_loaded'):
            self.load_config()
            self._config_loaded = True
    
    def load_config(self):
        """加载配置"""
        try:
            self.email_tab.load_config()
            self.webhook_tab.load_config()
            self.keyword_tab.load_config()
            self.general_tab.load_config()
            logger.info("配置加载完成")
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            QMessageBox.warning(self, "错误", f"加载配置失败: {e}")

    def force_reload_config(self):
        """强制重新加载配置"""
        self._config_loaded = False
        self.load_config()
        self._config_loaded = True
    
    def save_config(self):
        """保存配置"""
        try:
            # 验证配置
            if not self._validate_config():
                return
            
            # 保存各个选项卡的配置
            self.email_tab.save_config()
            self.webhook_tab.save_config()
            self.keyword_tab.save_config()
            self.general_tab.save_config()
            
            # 保存到文件
            config_manager.save_config()
            
            # 发送信号
            self.config_saved.emit()

            QMessageBox.information(self, "成功", "配置保存成功！")
            self.accept()

            logger.info("配置保存成功")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存配置失败: {e}")
    
    def _validate_config(self) -> bool:
        """验证配置"""
        # 验证邮箱配置
        if not self.email_tab.validate():
            self.tab_widget.setCurrentWidget(self.email_tab)
            return False
        
        # 验证Webhook配置
        if not self.webhook_tab.validate():
            self.tab_widget.setCurrentWidget(self.webhook_tab)
            return False
        
        # 验证关键字配置
        if not self.keyword_tab.validate():
            self.tab_widget.setCurrentWidget(self.keyword_tab)
            return False
        
        # 验证通用设置
        if not self.general_tab.validate():
            self.tab_widget.setCurrentWidget(self.general_tab)
            return False
        
        return True
    
    def test_connections(self):
        """测试连接"""
        current_tab = self.tab_widget.currentWidget()
        
        if current_tab == self.email_tab:
            self.email_tab.test_connections()
        elif current_tab == self.webhook_tab:
            self.webhook_tab.test_connections()
        else:
            QMessageBox.information(self, "提示", "当前选项卡不支持连接测试")
    
    def reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置当前选项卡的配置吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            current_tab = self.tab_widget.currentWidget()
            if hasattr(current_tab, 'reset_config'):
                current_tab.reset_config()
                QMessageBox.information(self, "成功", "配置已重置")
