# -*- coding: utf-8 -*-
"""
Webhook配置选项卡
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidget, 
                               QListWidgetItem, QPushButton, QGroupBox, QFormLayout,
                               QLineEdit, QTextEdit, QMessageBox, QLabel)
from PySide6.QtCore import Qt
from typing import List
from src.config.config_manager import config_manager
from src.pusher.dingtalk_pusher import DingTalkPusher
from src.utils.logger import get_logger

logger = get_logger(__name__)


class WebhookConfigTab(QWidget):
    """Webhook配置选项卡"""
    
    def __init__(self):
        super().__init__()
        self.webhooks: List[str] = []
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 说明文本
        info_label = QLabel(
            "配置钉钉机器人Webhook地址，用于推送告警邮件。\n"
            "示例: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key"
        )
        info_label.setStyleSheet("color: #666; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # Webhook列表
        self._create_webhook_list(layout)
        
        # 添加Webhook区域
        self._create_add_webhook_area(layout)
    
    def _create_webhook_list(self, parent_layout):
        """创建Webhook列表"""
        list_group = QGroupBox("已配置的Webhook")
        list_layout = QVBoxLayout(list_group)
        
        # Webhook列表
        self.webhook_list = QListWidget()
        self.webhook_list.setMinimumHeight(200)
        list_layout.addWidget(self.webhook_list)
        
        # 按钮组
        button_layout = QHBoxLayout()
        list_layout.addLayout(button_layout)
        
        self.test_button = QPushButton("🔧 测试选中")
        self.remove_button = QPushButton("➖ 删除选中")
        self.test_all_button = QPushButton("🔧 测试全部")
        
        self.test_button.setEnabled(False)
        self.remove_button.setEnabled(False)
        
        button_layout.addWidget(self.test_button)
        button_layout.addWidget(self.remove_button)
        button_layout.addStretch()
        button_layout.addWidget(self.test_all_button)
        
        parent_layout.addWidget(list_group)
    
    def _create_add_webhook_area(self, parent_layout):
        """创建添加Webhook区域"""
        add_group = QGroupBox("添加新Webhook")
        add_layout = QFormLayout(add_group)
        
        # Webhook URL输入
        self.webhook_edit = QLineEdit()
        self.webhook_edit.setPlaceholderText("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key")
        add_layout.addRow("Webhook URL:", self.webhook_edit)
        
        # 按钮组
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("➕ 添加")
        self.test_new_button = QPushButton("🔧 测试")
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.test_new_button)
        button_layout.addStretch()
        
        add_layout.addRow("", button_layout)
        
        parent_layout.addWidget(add_group)
    
    def _connect_signals(self):
        """连接信号"""
        self.webhook_list.currentRowChanged.connect(self.on_webhook_selected)
        self.add_button.clicked.connect(self.add_webhook)
        self.remove_button.clicked.connect(self.remove_webhook)
        self.test_button.clicked.connect(self.test_selected_webhook)
        self.test_new_button.clicked.connect(self.test_new_webhook)
        self.test_all_button.clicked.connect(self.test_all_webhooks)
        
        # 回车键添加Webhook
        self.webhook_edit.returnPressed.connect(self.add_webhook)
    
    def load_config(self):
        """加载配置"""
        self.webhooks = config_manager.get_webhooks().copy()
        self._update_webhook_list()
    
    def save_config(self):
        """保存配置"""
        # 清空现有配置
        config_manager.config["webhooks"] = []
        
        # 保存所有Webhook
        for webhook in self.webhooks:
            config_manager.add_webhook(webhook)
    
    def validate(self) -> bool:
        """验证配置"""
        if not self.webhooks:
            QMessageBox.warning(self, "验证失败", "至少需要配置一个Webhook地址")
            return False
        
        for webhook in self.webhooks:
            if not webhook.startswith(("http://", "https://")):
                QMessageBox.warning(self, "验证失败", f"无效的Webhook URL: {webhook}")
                return False
        
        return True
    
    def _update_webhook_list(self):
        """更新Webhook列表"""
        self.webhook_list.clear()
        
        for webhook in self.webhooks:
            # 显示简化的URL
            display_url = webhook
            if len(display_url) > 80:
                display_url = display_url[:77] + "..."
            
            item = QListWidgetItem(display_url)
            item.setToolTip(webhook)  # 完整URL作为提示
            self.webhook_list.addItem(item)
        
        # 更新按钮状态
        self.test_all_button.setEnabled(len(self.webhooks) > 0)
    
    def on_webhook_selected(self, row):
        """Webhook被选中"""
        has_selection = row >= 0
        self.test_button.setEnabled(has_selection)
        self.remove_button.setEnabled(has_selection)
    
    def add_webhook(self):
        """添加Webhook"""
        webhook_url = self.webhook_edit.text().strip()
        
        if not webhook_url:
            QMessageBox.warning(self, "输入错误", "请输入Webhook URL")
            return
        
        if not webhook_url.startswith(("http://", "https://")):
            QMessageBox.warning(self, "输入错误", "Webhook URL必须以http://或https://开头")
            return
        
        if webhook_url in self.webhooks:
            QMessageBox.warning(self, "重复添加", "该Webhook已存在")
            return
        
        self.webhooks.append(webhook_url)
        self._update_webhook_list()
        self.webhook_edit.clear()
        
        QMessageBox.information(self, "成功", "Webhook添加成功")
    
    def remove_webhook(self):
        """删除Webhook"""
        current_row = self.webhook_list.currentRow()
        if current_row < 0:
            return
        
        webhook_url = self.webhooks[current_row]
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除这个Webhook吗？\n{webhook_url}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            del self.webhooks[current_row]
            self._update_webhook_list()
            QMessageBox.information(self, "成功", "Webhook删除成功")
    
    def test_selected_webhook(self):
        """测试选中的Webhook"""
        current_row = self.webhook_list.currentRow()
        if current_row < 0:
            return
        
        webhook_url = self.webhooks[current_row]
        self._test_webhook(webhook_url)
    
    def test_new_webhook(self):
        """测试新输入的Webhook"""
        webhook_url = self.webhook_edit.text().strip()
        
        if not webhook_url:
            QMessageBox.warning(self, "输入错误", "请输入Webhook URL")
            return
        
        if not webhook_url.startswith(("http://", "https://")):
            QMessageBox.warning(self, "输入错误", "Webhook URL必须以http://或https://开头")
            return
        
        self._test_webhook(webhook_url)
    
    def test_all_webhooks(self):
        """测试所有Webhook"""
        if not self.webhooks:
            QMessageBox.information(self, "提示", "没有配置的Webhook")
            return
        
        success_count = 0
        total_count = len(self.webhooks)
        
        for webhook in self.webhooks:
            try:
                pusher = DingTalkPusher([webhook])
                success, message = pusher.test_webhook(webhook)
                if success:
                    success_count += 1
            except:
                pass
        
        QMessageBox.information(
            self, "测试结果", 
            f"Webhook测试完成\n成功: {success_count}/{total_count}"
        )
    
    def _test_webhook(self, webhook_url: str):
        """测试单个Webhook"""
        try:
            pusher = DingTalkPusher([webhook_url])
            success, message = pusher.test_webhook(webhook_url)
            
            if success:
                QMessageBox.information(self, "测试成功", f"Webhook测试成功: {message}")
            else:
                QMessageBox.warning(self, "测试失败", f"Webhook测试失败: {message}")
        
        except Exception as e:
            QMessageBox.critical(self, "测试错误", f"测试过程中发生错误: {str(e)}")
    
    def test_connections(self):
        """测试连接（供外部调用）"""
        self.test_all_webhooks()
    
    def reset_config(self):
        """重置配置"""
        self.webhooks = []
        self._update_webhook_list()
        self.webhook_edit.clear()
