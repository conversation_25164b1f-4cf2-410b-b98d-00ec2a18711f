#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件告警推送程序主入口
"""

import sys
from PySide6.QtWidgets import QApplication

from src.gui.main_window import MainWindow
from src.utils.logger import setup_logger


def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("邮件告警推送程序")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AlertPusher")
    
    # 设置应用程序图标
    # app.setWindowIcon(QIcon("resources/icon.ico"))
    
    # 设置中文本地化（简化版本）
    # 对于企业内部工具，使用系统默认即可
    
    # 初始化日志系统
    setup_logger()
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    # 运行应用程序
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
