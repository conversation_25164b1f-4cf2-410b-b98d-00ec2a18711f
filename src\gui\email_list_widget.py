# -*- coding: utf-8 -*-
"""
邮件列表组件
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                               QTableWidgetItem, QHeaderView, QPushButton, 
                               QLabel, QLineEdit, QComboBox, QMenu)
from PySide6.QtCore import Qt, Signal, QDateTime
from PySide6.QtGui import QFont, QColor, QAction
from typing import List, Optional
from src.email_handler.simple_email_client import EmailMessage
from src.utils.logger import get_logger

logger = get_logger(__name__)


class EmailListWidget(QWidget):
    """邮件列表组件"""
    
    # 信号定义
    email_selected = Signal(object)  # 邮件被选中
    push_requested = Signal(object)  # 请求推送邮件
    
    def __init__(self):
        super().__init__()
        self.emails: List[EmailMessage] = []
        self.filtered_emails: List[EmailMessage] = []
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建过滤器
        self._create_filter_bar(layout)
        
        # 创建邮件表格
        self._create_email_table(layout)
        
        # 创建底部信息栏
        self._create_info_bar(layout)
    
    def _create_filter_bar(self, parent_layout):
        """创建过滤器栏"""
        filter_layout = QHBoxLayout()
        parent_layout.addLayout(filter_layout)
        
        # 搜索框
        filter_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键字搜索邮件...")
        filter_layout.addWidget(self.search_edit)
        
        # 状态过滤
        filter_layout.addWidget(QLabel("状态:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部", "新邮件", "已推送", "未推送"])
        filter_layout.addWidget(self.status_combo)
        
        # 清除按钮
        clear_button = QPushButton("清除")
        clear_button.clicked.connect(self.clear_filter)
        filter_layout.addWidget(clear_button)
    
    def _create_email_table(self, parent_layout):
        """创建邮件表格"""
        self.email_table = QTableWidget()
        self.email_table.setColumnCount(5)
        self.email_table.setHorizontalHeaderLabels([
            "状态", "主题", "发件人", "时间", "推送状态"
        ])
        
        # 设置表格属性
        self.email_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.email_table.setSelectionMode(QTableWidget.SingleSelection)
        self.email_table.setAlternatingRowColors(True)
        self.email_table.setSortingEnabled(True)
        
        # 设置列宽
        header = self.email_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 状态列固定宽度
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 主题列自适应
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 发件人列适应内容
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 时间列适应内容
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 推送状态列固定宽度
        
        self.email_table.setColumnWidth(0, 60)
        self.email_table.setColumnWidth(4, 80)
        
        # 设置右键菜单
        self.email_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.email_table.customContextMenuRequested.connect(self.show_context_menu)
        
        parent_layout.addWidget(self.email_table)
    
    def _create_info_bar(self, parent_layout):
        """创建信息栏"""
        info_layout = QHBoxLayout()
        parent_layout.addLayout(info_layout)
        
        self.info_label = QLabel("邮件: 0")
        info_layout.addWidget(self.info_label)
        
        info_layout.addStretch()
        
        # 批量操作按钮
        self.batch_push_button = QPushButton("批量推送")
        self.batch_push_button.setEnabled(False)
        info_layout.addWidget(self.batch_push_button)
    
    def _connect_signals(self):
        """连接信号"""
        self.email_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.search_edit.textChanged.connect(self.apply_filter)
        self.status_combo.currentTextChanged.connect(self.apply_filter)
    
    def update_emails(self, emails: List[EmailMessage]):
        """更新邮件列表"""
        self.emails = emails
        self.apply_filter()
        logger.info(f"邮件列表已更新，共 {len(emails)} 封邮件")
    
    def apply_filter(self):
        """应用过滤器"""
        search_text = self.search_edit.text().lower()
        status_filter = self.status_combo.currentText()
        
        self.filtered_emails = []
        
        for email in self.emails:
            # 文本搜索
            if search_text:
                if (search_text not in email.subject.lower() and 
                    search_text not in email.sender.lower() and 
                    search_text not in email.content.lower()):
                    continue
            
            # 状态过滤
            if status_filter == "新邮件" and not email.is_new:
                continue
            elif status_filter == "已推送" and not email.is_pushed:
                continue
            elif status_filter == "未推送" and email.is_pushed:
                continue
            
            self.filtered_emails.append(email)
        
        self._update_table()
    
    def _update_table(self):
        """更新表格显示"""
        self.email_table.setRowCount(len(self.filtered_emails))
        
        for row, email in enumerate(self.filtered_emails):
            # 状态列
            status_item = QTableWidgetItem("🆕" if email.is_new else "📧")
            status_item.setTextAlignment(Qt.AlignCenter)
            self.email_table.setItem(row, 0, status_item)
            
            # 主题列
            subject_item = QTableWidgetItem(email.subject)
            if email.is_new:
                font = QFont()
                font.setBold(True)
                subject_item.setFont(font)
                subject_item.setForeground(QColor(0, 100, 200))
            self.email_table.setItem(row, 1, subject_item)
            
            # 发件人列
            sender_item = QTableWidgetItem(email.sender)
            self.email_table.setItem(row, 2, sender_item)
            
            # 时间列
            time_str = email.date.strftime("%m-%d %H:%M")
            time_item = QTableWidgetItem(time_str)
            self.email_table.setItem(row, 3, time_item)
            
            # 推送状态列
            push_status = "✅" if email.is_pushed else "⏸️"
            push_item = QTableWidgetItem(push_status)
            push_item.setTextAlignment(Qt.AlignCenter)
            self.email_table.setItem(row, 4, push_item)
        
        # 更新信息栏
        total_count = len(self.emails)
        filtered_count = len(self.filtered_emails)
        new_count = sum(1 for email in self.filtered_emails if email.is_new)
        
        info_text = f"邮件: {filtered_count}"
        if filtered_count != total_count:
            info_text += f"/{total_count}"
        if new_count > 0:
            info_text += f" (新邮件: {new_count})"
        
        self.info_label.setText(info_text)
    
    def on_selection_changed(self):
        """选择变化"""
        selected_email = self.get_selected_email()
        self.email_selected.emit(selected_email)
    
    def get_selected_email(self) -> Optional[EmailMessage]:
        """获取选中的邮件"""
        current_row = self.email_table.currentRow()
        if 0 <= current_row < len(self.filtered_emails):
            return self.filtered_emails[current_row]
        return None
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.email_table.itemAt(position) is None:
            return
        
        selected_email = self.get_selected_email()
        if not selected_email:
            return
        
        menu = QMenu(self)
        
        # 推送邮件
        push_action = QAction("📤 推送此邮件", self)
        push_action.triggered.connect(lambda: self.push_requested.emit(selected_email))
        menu.addAction(push_action)
        
        menu.addSeparator()
        
        # 标记为已读
        if selected_email.is_new:
            mark_read_action = QAction("✅ 标记为已读", self)
            mark_read_action.triggered.connect(lambda: self.mark_as_read(selected_email))
            menu.addAction(mark_read_action)
        
        # 复制主题
        copy_subject_action = QAction("📋 复制主题", self)
        copy_subject_action.triggered.connect(lambda: self.copy_to_clipboard(selected_email.subject))
        menu.addAction(copy_subject_action)
        
        menu.exec(self.email_table.mapToGlobal(position))
    
    def mark_as_read(self, email_msg: EmailMessage):
        """标记为已读"""
        email_msg.is_new = False
        self._update_table()
    
    def copy_to_clipboard(self, text: str):
        """复制到剪贴板"""
        from PySide6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(text)
    
    def clear_filter(self):
        """清除过滤器"""
        self.search_edit.clear()
        self.status_combo.setCurrentIndex(0)
