# -*- coding: utf-8 -*-
"""
邮件详情组件
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QTextEdit, QFrame, QScrollArea, QPushButton)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor
from typing import Optional
from src.email_handler.simple_email_client import EmailMessage
from src.utils.logger import get_logger

logger = get_logger(__name__)


class EmailDetailWidget(QWidget):
    """邮件详情组件"""

    # 信号定义
    push_requested = Signal(object)  # 请求推送邮件

    def __init__(self):
        super().__init__()
        self.current_email: Optional[EmailMessage] = None

        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建标题栏
        self._create_header(layout)
        
        # 创建分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        
        # 创建邮件信息区域
        self._create_info_area(layout)
        
        # 创建分隔线
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line2)
        
        # 创建邮件内容区域
        self._create_content_area(layout)
        
        # 创建操作按钮区域
        self._create_action_area(layout)
        
        # 初始显示空状态
        self.show_empty_state()
    
    def _create_header(self, parent_layout):
        """创建标题栏"""
        header_layout = QHBoxLayout()
        parent_layout.addLayout(header_layout)
        
        # 标题
        title_label = QLabel("📧 邮件详情")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 状态标签
        self.status_label = QLabel("")
        header_layout.addWidget(self.status_label)
    
    def _create_info_area(self, parent_layout):
        """创建邮件信息区域"""
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(10, 10, 10, 10)
        
        # 主题
        self.subject_label = QLabel("")
        subject_font = QFont()
        subject_font.setPointSize(11)
        subject_font.setBold(True)
        self.subject_label.setFont(subject_font)
        self.subject_label.setWordWrap(True)
        info_layout.addWidget(self.subject_label)
        
        # 发件人和时间
        sender_time_layout = QHBoxLayout()
        info_layout.addLayout(sender_time_layout)
        
        self.sender_label = QLabel("")
        sender_time_layout.addWidget(self.sender_label)
        
        sender_time_layout.addStretch()
        
        self.time_label = QLabel("")
        self.time_label.setStyleSheet("color: #666;")
        sender_time_layout.addWidget(self.time_label)
        
        parent_layout.addWidget(info_widget)
    
    def _create_content_area(self, parent_layout):
        """创建邮件内容区域"""
        # 内容标签
        content_header_layout = QHBoxLayout()
        parent_layout.addLayout(content_header_layout)
        
        content_label = QLabel("📄 邮件内容")
        content_font = QFont()
        content_font.setBold(True)
        content_label.setFont(content_font)
        content_header_layout.addWidget(content_label)
        
        content_header_layout.addStretch()
        
        # 字符数统计
        self.char_count_label = QLabel("")
        self.char_count_label.setStyleSheet("color: #666; font-size: 10px;")
        content_header_layout.addWidget(self.char_count_label)
        
        # 内容文本框
        self.content_text = QTextEdit()
        self.content_text.setReadOnly(True)
        self.content_text.setMinimumHeight(200)
        
        # 设置字体
        content_font = QFont("Consolas, Monaco, monospace")
        content_font.setPointSize(9)
        self.content_text.setFont(content_font)
        
        parent_layout.addWidget(self.content_text)
    
    def _create_action_area(self, parent_layout):
        """创建操作按钮区域"""
        action_layout = QHBoxLayout()
        parent_layout.addLayout(action_layout)
        
        action_layout.addStretch()
        
        # 复制内容按钮
        self.copy_button = QPushButton("📋 复制内容")
        self.copy_button.setEnabled(False)
        self.copy_button.clicked.connect(self.copy_content)
        action_layout.addWidget(self.copy_button)
        
        # 推送按钮
        self.push_button = QPushButton("📤 推送此邮件")
        self.push_button.setEnabled(False)
        self.push_button.clicked.connect(self.push_current_email)
        action_layout.addWidget(self.push_button)
    
    def show_email(self, email_msg: Optional[EmailMessage]):
        """显示邮件详情"""
        self.current_email = email_msg
        
        if email_msg is None:
            self.show_empty_state()
            return
        
        # 更新状态标签
        status_parts = []
        if email_msg.is_new:
            status_parts.append("🆕 新邮件")
        if email_msg.is_pushed:
            status_parts.append("✅ 已推送")
        else:
            status_parts.append("⏸️ 未推送")
        
        self.status_label.setText(" | ".join(status_parts))
        
        # 更新主题
        self.subject_label.setText(email_msg.subject or "(无主题)")
        if email_msg.is_new:
            self.subject_label.setStyleSheet("color: #0066CC;")
        else:
            self.subject_label.setStyleSheet("")
        
        # 更新发件人
        self.sender_label.setText(f"发件人: {email_msg.sender}")
        
        # 更新时间
        time_str = email_msg.date.strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(time_str)
        
        # 更新内容
        content = email_msg.content or "(无内容)"
        self.content_text.setPlainText(content)
        
        # 更新字符数统计
        char_count = len(content)
        self.char_count_label.setText(f"字符数: {char_count}")
        
        # 启用按钮
        self.copy_button.setEnabled(True)
        self.push_button.setEnabled(True)
        
        logger.debug(f"显示邮件详情: {email_msg.subject}")
    
    def show_empty_state(self):
        """显示空状态"""
        self.status_label.setText("")
        self.subject_label.setText("请选择一封邮件查看详情")
        self.subject_label.setStyleSheet("color: #999; font-style: italic;")
        self.sender_label.setText("")
        self.time_label.setText("")
        self.content_text.setPlainText("")
        self.char_count_label.setText("")
        
        # 禁用按钮
        self.copy_button.setEnabled(False)
        self.push_button.setEnabled(False)
    
    def copy_content(self):
        """复制邮件内容到剪贴板"""
        if self.current_email:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            
            # 构建完整的邮件信息
            full_content = f"主题: {self.current_email.subject}\n"
            full_content += f"发件人: {self.current_email.sender}\n"
            full_content += f"时间: {self.current_email.date.strftime('%Y-%m-%d %H:%M:%S')}\n"
            full_content += f"\n内容:\n{self.current_email.content}"
            
            clipboard.setText(full_content)
            
            # 临时显示复制成功提示
            original_text = self.copy_button.text()
            self.copy_button.setText("✅ 已复制")
            self.copy_button.setEnabled(False)
            
            # 2秒后恢复按钮状态
            from PySide6.QtCore import QTimer
            QTimer.singleShot(2000, lambda: (
                self.copy_button.setText(original_text),
                self.copy_button.setEnabled(True)
            ))
    
    def get_current_email(self) -> Optional[EmailMessage]:
        """获取当前显示的邮件"""
        return self.current_email

    def push_current_email(self):
        """推送当前邮件"""
        if self.current_email:
            self.push_requested.emit(self.current_email)
