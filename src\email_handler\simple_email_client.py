#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的邮件客户端 - <PERSON><PERSON>式设计
只负责获取邮件，不判断新旧，不管理状态
"""

import imaplib
import email
import re
from datetime import datetime, timezone
from email.utils import parsedate_to_datetime
from typing import List, Dict, Any, Optional
from src.utils.logger import get_logger

logger = get_logger(__name__)


class EmailMessage:
    """邮件消息类"""

    def __init__(self, uid: str, subject: str, sender: str, date, content: str, is_new: bool = False):
        self.uid = uid
        self.subject = subject
        self.sender = sender
        self.date = date
        self.content = content
        self.is_new = is_new
        self.is_pushed = False


class SimpleEmailClient:
    """简化的邮件客户端 - 只负责获取邮件"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.imap = None
        self.is_connected = False
        
        # 连接管理
        self.connection_timeout = 30
        self.max_retries = 3
    
    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        self.disconnect()
    
    def connect(self) -> bool:
        """连接到邮件服务器，带重试机制"""
        for attempt in range(self.max_retries):
            try:
                if self.is_connected and self._test_connection():
                    return True
                
                # 清理旧连接
                self.disconnect()
                
                server = self.config.get("server")
                port = self.config.get("port", 993)
                email_addr = self.config.get("email")
                password = self.config.get("password")
                
                if not all([server, email_addr, password]):
                    logger.error("邮箱配置不完整")
                    return False
                
                logger.info(f"尝试连接到邮箱: {email_addr} (尝试 {attempt + 1}/{self.max_retries})")
                
                # 创建IMAP连接，设置超时
                if self.config.get("use_ssl", True):
                    self.imap = imaplib.IMAP4_SSL(server, port, timeout=self.connection_timeout)
                else:
                    self.imap = imaplib.IMAP4(server, port, timeout=self.connection_timeout)
                    if self.config.get("use_tls", True):
                        self.imap.starttls()
                
                # 登录
                from src.config.config_manager import config_manager
                decrypted_password = config_manager.decrypt_password(password)
                self.imap.login(email_addr, decrypted_password)
                
                # 选择收件箱
                self.imap.select("INBOX")
                
                self.is_connected = True
                logger.info(f"成功连接到邮箱: {email_addr}")
                return True
                
            except Exception as e:
                logger.warning(f"连接邮箱失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                self.is_connected = False
                
                # 清理失败的连接
                if self.imap:
                    try:
                        self.imap.close()
                        self.imap.logout()
                    except:
                        pass
                    self.imap = None
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.max_retries - 1:
                    import time
                    wait_time = 2 ** attempt  # 指数退避
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
        
        logger.error(f"连接邮箱失败，已尝试 {self.max_retries} 次")
        return False
    
    def disconnect(self):
        """安全断开连接"""
        if self.imap:
            try:
                if self.is_connected:
                    try:
                        self.imap.close()
                    except Exception as e:
                        logger.debug(f"关闭IMAP连接时出错: {e}")
                    
                    try:
                        self.imap.logout()
                    except Exception as e:
                        logger.debug(f"登出IMAP时出错: {e}")
                
            except Exception as e:
                logger.debug(f"断开连接时出错: {e}")
            finally:
                self.imap = None
                self.is_connected = False
                logger.debug(f"已断开邮箱连接: {self.config.get('email', 'unknown')}")
    
    def _test_connection(self) -> bool:
        """测试IMAP连接是否有效"""
        try:
            if self.imap:
                self.imap.noop()
                return True
        except Exception as e:
            logger.debug(f"连接测试失败: {e}")
            return False
        return False
    
    def fetch_emails(self, limit: int = 50) -> List[EmailMessage]:
        """获取邮件列表 - 高性能版本，只获取必要信息"""
        if not self.connect():
            return []

        try:
            # 搜索所有邮件
            status, messages = self.imap.search(None, "ALL")
            if status != "OK":
                logger.error("搜索邮件失败")
                return []

            message_ids = messages[0].split()
            logger.debug(f"找到 {len(message_ids)} 封邮件")

            # 限制获取的邮件数量（减少默认数量提高性能）
            max_emails = min(limit, len(message_ids))
            if len(message_ids) > max_emails:
                logger.info(f"邮件数量过多({len(message_ids)})，只获取最新的{max_emails}封")
                message_ids = message_ids[-max_emails:]

            # 批量获取邮件头信息（性能优化）
            emails = self._fetch_emails_batch(message_ids)

            logger.info(f"成功获取 {len(emails)} 封邮件")
            return emails

        except Exception as e:
            logger.error(f"获取邮件列表失败: {e}")
            return []

    def _fetch_emails_batch(self, message_ids: List[bytes]) -> List[EmailMessage]:
        """批量获取邮件 - 性能优化"""
        emails = []

        # 批量获取邮件头信息，不获取邮件体（大幅提升性能）
        for msg_id in reversed(message_ids):  # 最新的邮件在前
            try:
                email_msg = self._fetch_single_email_fast(msg_id)
                if email_msg:
                    emails.append(email_msg)

            except Exception as e:
                logger.error(f"获取邮件失败 {msg_id}: {e}")
                continue

        return emails
    
    def _fetch_single_email_fast(self, msg_id: bytes) -> Optional[EmailMessage]:
        """快速获取单个邮件 - 只获取头信息，不获取邮件体"""
        try:
            # 获取UID和邮件头信息（不获取邮件体，大幅提升性能）
            status, data = self.imap.fetch(msg_id, '(UID BODY[HEADER.FIELDS (SUBJECT FROM DATE)])')
            if status != "OK":
                return None

            # 解析UID
            uid_match = re.search(r'UID (\d+)', data[0][0].decode())
            uid = uid_match.group(1) if uid_match else msg_id.decode()

            # 解析邮件头
            header_data = data[0][1]
            if isinstance(header_data, bytes):
                header_text = header_data.decode('utf-8', errors='ignore')
            else:
                header_text = str(header_data)

            # 提取邮件头信息
            subject = ""
            sender = ""
            date_str = ""

            for line in header_text.split('\n'):
                line = line.strip()
                if line.lower().startswith('subject:'):
                    subject = self._decode_header(line[8:].strip())
                elif line.lower().startswith('from:'):
                    sender = self._decode_header(line[5:].strip())
                elif line.lower().startswith('date:'):
                    date_str = line[5:].strip()

            # 解析日期
            try:
                if date_str:
                    date = parsedate_to_datetime(date_str)
                    if date.tzinfo is None:
                        date = date.replace(tzinfo=timezone.utc)
                else:
                    date = datetime.now(timezone.utc)
            except Exception as e:
                logger.debug(f"解析邮件日期失败: {e}")
                date = datetime.now(timezone.utc)

            # 对于列表显示，不需要获取邮件内容（按需加载）
            return EmailMessage(
                uid=uid,
                subject=subject or "(无主题)",
                sender=sender or "(未知发件人)",
                date=date,
                content=""  # 列表显示时不需要内容，点击时再加载
            )

        except Exception as e:
            logger.error(f"快速获取邮件失败: {e}")
            return None

    def _fetch_single_email(self, msg_id: bytes) -> Optional[EmailMessage]:
        """获取单个邮件完整信息（包含内容）"""
        try:
            # 获取UID
            status, uid_data = self.imap.fetch(msg_id, '(UID)')
            if status == 'OK':
                uid_match = re.search(r'UID (\d+)', uid_data[0].decode())
                uid = uid_match.group(1) if uid_match else msg_id.decode()
            else:
                uid = msg_id.decode()

            # 获取邮件数据
            status, msg_data = self.imap.fetch(msg_id, "(RFC822)")
            if status != "OK":
                return None

            # 解析邮件
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)

            # 解析邮件头
            subject = self._decode_header(email_message.get("Subject", ""))
            sender = self._decode_header(email_message.get("From", ""))
            date_str = email_message.get("Date", "")

            # 解析日期
            try:
                if date_str:
                    date = parsedate_to_datetime(date_str)
                    # 如果日期没有时区信息，添加UTC时区
                    if date.tzinfo is None:
                        date = date.replace(tzinfo=timezone.utc)
                else:
                    date = datetime.now(timezone.utc)
            except Exception as e:
                logger.debug(f"解析邮件日期失败: {e}")
                date = datetime.now(timezone.utc)

            # 获取邮件内容
            content = self._get_email_content(email_message)

            return EmailMessage(
                uid=uid,
                subject=subject,
                sender=sender,
                date=date,
                content=content
            )

        except Exception as e:
            logger.error(f"解析邮件失败: {e}")
            return None

    def fetch_email_content_by_uid(self, uid: str) -> str:
        """根据UID获取邮件完整内容"""
        try:
            if not self.connect():
                return ""

            # 根据UID搜索邮件
            status, msg_ids = self.imap.uid('search', None, f'UID {uid}')
            if status != "OK" or not msg_ids[0]:
                logger.debug(f"未找到UID为{uid}的邮件")
                return ""

            # 获取完整邮件内容
            status, msg_data = self.imap.uid('fetch', uid, "(RFC822)")
            if status != "OK":
                logger.debug(f"获取UID为{uid}的邮件内容失败")
                return ""

            # 解析邮件
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)

            # 获取邮件内容
            content = self._get_email_content(email_message)
            return content

        except Exception as e:
            logger.error(f"根据UID获取邮件内容失败: {e}")
            return ""

    def _decode_header(self, header: str) -> str:
        """解码邮件头"""
        if not header:
            return ""
        
        try:
            from email.header import decode_header
            decoded_parts = decode_header(header)
            result = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        result += part.decode(encoding)
                    else:
                        result += part.decode('utf-8', errors='ignore')
                else:
                    result += part
            
            return result.strip()
            
        except Exception as e:
            logger.debug(f"解码邮件头失败: {e}")
            return str(header)
    
    def _get_email_content(self, email_message) -> str:
        """获取邮件内容"""
        try:
            content = ""
            
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    
                    if content_type in ["text/plain", "text/html"]:
                        charset = part.get_content_charset() or 'utf-8'
                        payload = part.get_payload(decode=True)
                        
                        if payload:
                            try:
                                text = payload.decode(charset, errors='ignore')
                                content += text + "\n"
                            except Exception as e:
                                logger.debug(f"解码邮件内容失败: {e}")
            else:
                charset = email_message.get_content_charset() or 'utf-8'
                payload = email_message.get_payload(decode=True)
                
                if payload:
                    try:
                        content = payload.decode(charset, errors='ignore')
                    except Exception as e:
                        logger.debug(f"解码邮件内容失败: {e}")
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"获取邮件内容失败: {e}")
            return ""
