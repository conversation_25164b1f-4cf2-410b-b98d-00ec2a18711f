# -*- coding: utf-8 -*-
"""
邮箱配置选项卡
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidget,
                               QListWidgetItem, QPushButton, QGroupBox, QFormLayout,
                               QLineEdit, QSpinBox, QCheckBox, QComboBox, QMessageBox, QLabel)
from PySide6.QtCore import Qt
from typing import List, Dict, Any
from src.config.config_manager import config_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class EmailConfigTab(QWidget):
    """邮箱配置选项卡"""
    
    def __init__(self):
        super().__init__()
        self.email_accounts: List[Dict[str, Any]] = []
        self.current_account_index = -1
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        
        # 左侧：邮箱列表
        self._create_account_list(layout)
        
        # 右侧：邮箱配置
        self._create_account_config(layout)
    
    def _create_account_list(self, parent_layout):
        """创建邮箱列表"""
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        
        # 列表标题
        list_layout.addWidget(QLabel("邮箱账户"))
        
        # 邮箱列表
        self.account_list = QListWidget()
        self.account_list.setMaximumWidth(250)
        list_layout.addWidget(self.account_list)
        
        # 按钮组
        button_layout = QHBoxLayout()
        list_layout.addLayout(button_layout)
        
        self.add_button = QPushButton("➕ 添加")
        self.remove_button = QPushButton("➖ 删除")
        self.remove_button.setEnabled(False)
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.remove_button)
        
        parent_layout.addWidget(list_widget)
    
    def _create_account_config(self, parent_layout):
        """创建邮箱配置"""
        config_group = QGroupBox("邮箱配置")
        config_layout = QFormLayout(config_group)
        
        # 邮箱地址
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        config_layout.addRow("邮箱地址:", self.email_edit)
        
        # 密码/授权码
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("邮箱密码或授权码")
        config_layout.addRow("密码/授权码:", self.password_edit)
        
        # IMAP服务器
        self.server_edit = QLineEdit()
        self.server_edit.setPlaceholderText("imap.gmail.com")
        config_layout.addRow("IMAP服务器:", self.server_edit)
        
        # 端口
        self.port_spin = QSpinBox()
        self.port_spin.setRange(1, 65535)
        self.port_spin.setValue(993)
        config_layout.addRow("端口:", self.port_spin)
        
        # 加密方式
        self.encryption_combo = QComboBox()
        self.encryption_combo.addItems(["SSL/TLS", "STARTTLS", "无加密"])
        config_layout.addRow("加密方式:", self.encryption_combo)
        
        # 启用状态
        self.enabled_check = QCheckBox("启用此邮箱")
        self.enabled_check.setChecked(True)
        config_layout.addRow("", self.enabled_check)
        
        # 测试连接按钮
        self.test_button = QPushButton("🔧 测试连接")
        config_layout.addRow("", self.test_button)
        
        # 保存按钮
        button_layout = QHBoxLayout()
        self.save_account_button = QPushButton("💾 保存账户")
        self.cancel_account_button = QPushButton("❌取消")
        button_layout.addWidget(self.save_account_button)
        button_layout.addWidget(self.cancel_account_button)
        config_layout.addRow("", button_layout)
        
        # 初始状态禁用配置区域
        config_group.setEnabled(False)
        self.config_group = config_group
        
        parent_layout.addWidget(config_group)
    
    def _connect_signals(self):
        """连接信号"""
        self.account_list.currentRowChanged.connect(self.on_account_selected)
        self.add_button.clicked.connect(self.add_account)
        self.remove_button.clicked.connect(self.remove_account)
        self.save_account_button.clicked.connect(self.save_current_account)
        self.cancel_account_button.clicked.connect(self.cancel_edit)
        self.test_button.clicked.connect(self.test_current_account)
        
        # 邮箱地址变化时自动填充服务器配置
        self.email_edit.textChanged.connect(self.auto_fill_server_config)
    
    def load_config(self):
        """加载配置"""
        self.email_accounts = config_manager.get_email_accounts().copy()
        self._update_account_list()
    
    def save_config(self):
        """保存配置"""
        # 清空现有配置
        config_manager.config["email_accounts"] = []
        
        # 保存所有账户
        for account in self.email_accounts:
            config_manager.add_email_account(account.copy())
    
    def validate(self) -> bool:
        """验证配置"""
        if not self.email_accounts:
            QMessageBox.warning(self, "验证失败", "至少需要配置一个邮箱账户")
            return False
        
        for i, account in enumerate(self.email_accounts):
            if not account.get("email"):
                QMessageBox.warning(self, "验证失败", f"第{i+1}个邮箱账户的邮箱地址不能为空")
                return False
            if not account.get("password"):
                QMessageBox.warning(self, "验证失败", f"第{i+1}个邮箱账户的密码不能为空")
                return False
            if not account.get("server"):
                QMessageBox.warning(self, "验证失败", f"第{i+1}个邮箱账户的服务器地址不能为空")
                return False
        
        return True
    
    def _update_account_list(self):
        """更新账户列表"""
        self.account_list.clear()
        
        for i, account in enumerate(self.email_accounts):
            item_text = account.get("email", f"账户{i+1}")
            if not account.get("enabled", True):
                item_text += " (已禁用)"
            
            item = QListWidgetItem(item_text)
            self.account_list.addItem(item)
    
    def on_account_selected(self, row):
        """账户被选中"""
        if 0 <= row < len(self.email_accounts):
            self.current_account_index = row
            self._load_account_config(self.email_accounts[row])
            self.config_group.setEnabled(True)
            self.remove_button.setEnabled(True)
        else:
            self.current_account_index = -1
            self._clear_account_config()
            self.config_group.setEnabled(False)
            self.remove_button.setEnabled(False)
    
    def _load_account_config(self, account: Dict[str, Any]):
        """加载账户配置到界面"""
        self.email_edit.setText(account.get("email", ""))
        # 如果有密码，显示占位符；如果没有密码，保持为空
        if account.get("password"):
            self.password_edit.setText("••••••••••••••••")  # 显示占位符表示已保存密码
            self.password_edit.setPlaceholderText("密码已保存，如需修改请重新输入")
        else:
            self.password_edit.setText("")
            self.password_edit.setPlaceholderText("邮箱密码或授权码")
        self.server_edit.setText(account.get("server", ""))
        self.port_spin.setValue(account.get("port", 993))
        
        # 设置加密方式
        if account.get("use_ssl", True):
            self.encryption_combo.setCurrentText("SSL/TLS")
        elif account.get("use_tls", False):
            self.encryption_combo.setCurrentText("STARTTLS")
        else:
            self.encryption_combo.setCurrentText("无加密")
        
        self.enabled_check.setChecked(account.get("enabled", True))
    
    def _clear_account_config(self):
        """清空账户配置"""
        self.email_edit.clear()
        self.password_edit.clear()
        self.server_edit.clear()
        self.port_spin.setValue(993)
        self.encryption_combo.setCurrentIndex(0)
        self.enabled_check.setChecked(True)
    
    def add_account(self):
        """添加账户"""
        self.current_account_index = len(self.email_accounts)
        self.email_accounts.append({})
        self._update_account_list()
        self.account_list.setCurrentRow(self.current_account_index)
        self._clear_account_config()
        self.config_group.setEnabled(True)
    
    def remove_account(self):
        """删除账户"""
        if self.current_account_index >= 0:
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除邮箱账户 '{self.email_accounts[self.current_account_index].get('email', '未命名')}' 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                del self.email_accounts[self.current_account_index]
                self._update_account_list()
                self.current_account_index = -1
                self._clear_account_config()
                self.config_group.setEnabled(False)
                self.remove_button.setEnabled(False)

    def save_current_account(self):
        """保存当前账户"""
        if self.current_account_index < 0:
            return

        # 验证输入
        if not self.email_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "邮箱地址不能为空")
            return

        password_text = self.password_edit.text().strip()

        # 检查密码
        if not password_text:
            QMessageBox.warning(self, "验证失败", "密码不能为空")
            return

        if not self.server_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "服务器地址不能为空")
            return

        # 构建账户配置
        account = {
            "email": self.email_edit.text().strip(),
            "server": self.server_edit.text().strip(),
            "port": self.port_spin.value(),
            "enabled": self.enabled_check.isChecked()
        }

        # 处理密码：如果是占位符，保持原密码；否则加密新密码
        if password_text == "••••••••••••••••":
            # 保持原密码
            if self.current_account_index < len(self.email_accounts):
                account["password"] = self.email_accounts[self.current_account_index].get("password", "")
        else:
            # 加密新密码
            account["password"] = config_manager.encrypt_password(password_text)

        # 设置加密方式
        encryption = self.encryption_combo.currentText()
        if encryption == "SSL/TLS":
            account["use_ssl"] = True
            account["use_tls"] = False
        elif encryption == "STARTTLS":
            account["use_ssl"] = False
            account["use_tls"] = True
        else:
            account["use_ssl"] = False
            account["use_tls"] = False

        # 保存到列表
        self.email_accounts[self.current_account_index] = account
        self._update_account_list()

        QMessageBox.information(self, "成功", "账户配置已保存")

    def cancel_edit(self):
        """取消编辑"""
        if self.current_account_index >= 0:
            if self.current_account_index < len(self.email_accounts):
                self._load_account_config(self.email_accounts[self.current_account_index])
            else:
                # 新添加的账户，删除它
                self.email_accounts.pop()
                self._update_account_list()
                self.current_account_index = -1
                self._clear_account_config()
                self.config_group.setEnabled(False)

    def test_current_account(self):
        """测试当前账户连接"""
        if not self.email_edit.text().strip():
            QMessageBox.warning(self, "测试失败", "请先填写邮箱地址")
            return

        if not self.password_edit.text().strip():
            QMessageBox.warning(self, "测试失败", "请先填写密码")
            return

        if not self.server_edit.text().strip():
            QMessageBox.warning(self, "测试失败", "请先填写服务器地址")
            return

        # 构建测试配置
        test_config = {
            "email": self.email_edit.text().strip(),
            "password": config_manager.encrypt_password(self.password_edit.text().strip()),
            "server": self.server_edit.text().strip(),
            "port": self.port_spin.value()
        }

        encryption = self.encryption_combo.currentText()
        if encryption == "SSL/TLS":
            test_config["use_ssl"] = True
            test_config["use_tls"] = False
        elif encryption == "STARTTLS":
            test_config["use_ssl"] = False
            test_config["use_tls"] = True
        else:
            test_config["use_ssl"] = False
            test_config["use_tls"] = False

        # 执行测试
        self.test_button.setEnabled(False)
        self.test_button.setText("测试中...")

        try:
            from src.email_handler.simple_email_client import SimpleEmailClient
            client = SimpleEmailClient(test_config)
            # 尝试连接并获取邮件数量
            emails = client.fetch_emails()
            success = True
            message = f"连接成功，找到 {len(emails)} 封邮件"

            if success:
                QMessageBox.information(self, "测试成功", f"连接测试成功: {message}")
            else:
                QMessageBox.warning(self, "测试失败", f"连接测试失败: {message}")

        except Exception as e:
            QMessageBox.critical(self, "测试错误", f"测试过程中发生错误: {str(e)}")

        finally:
            self.test_button.setEnabled(True)
            self.test_button.setText("🔧 测试连接")

    def auto_fill_server_config(self, email_text):
        """根据邮箱地址自动填充服务器配置"""
        if "@" not in email_text:
            return

        domain = email_text.split("@")[-1].lower()

        # 常见邮箱服务商配置
        server_configs = {
            "gmail.com": {"server": "imap.gmail.com", "port": 993, "ssl": True},
            "outlook.com": {"server": "outlook.office365.com", "port": 993, "ssl": True},
            "hotmail.com": {"server": "outlook.office365.com", "port": 993, "ssl": True},
            "live.com": {"server": "outlook.office365.com", "port": 993, "ssl": True},
            "qq.com": {"server": "imap.qq.com", "port": 993, "ssl": True},
            "163.com": {"server": "imap.163.com", "port": 993, "ssl": True},
            "126.com": {"server": "imap.126.com", "port": 993, "ssl": True},
            "sina.com": {"server": "imap.sina.com", "port": 993, "ssl": True},
            "yahoo.com": {"server": "imap.mail.yahoo.com", "port": 993, "ssl": True},
        }

        if domain in server_configs:
            config = server_configs[domain]
            if not self.server_edit.text():  # 只在服务器地址为空时自动填充
                self.server_edit.setText(config["server"])
                self.port_spin.setValue(config["port"])
                if config["ssl"]:
                    self.encryption_combo.setCurrentText("SSL/TLS")

    def test_connections(self):
        """测试所有连接"""
        if not self.email_accounts:
            QMessageBox.information(self, "提示", "没有配置的邮箱账户")
            return

        success_count = 0
        total_count = len([acc for acc in self.email_accounts if acc.get("enabled", True)])

        for account in self.email_accounts:
            if not account.get("enabled", True):
                continue

            try:
                from src.email_handler.simple_email_client import SimpleEmailClient
                client = SimpleEmailClient(account)
                emails = client.fetch_emails()
                success_count += 1
            except:
                pass

        QMessageBox.information(
            self, "测试结果",
            f"连接测试完成\n成功: {success_count}/{total_count}"
        )

    def reset_config(self):
        """重置配置"""
        self.email_accounts = []
        self._update_account_list()
        self.current_account_index = -1
        self._clear_account_config()
        self.config_group.setEnabled(False)
        self.remove_button.setEnabled(False)
