# -*- coding: utf-8 -*-
"""
关键字配置选项卡
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidget, 
                               QListWidgetItem, QPushButton, QGroupBox, QFormLayout,
                               QLineEdit, QTextEdit, QMessageBox, QLabel, QCheckBox)
from PySide6.QtCore import Qt
from typing import List
from src.config.config_manager import config_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class KeywordConfigTab(QWidget):
    """关键字配置选项卡"""
    
    def __init__(self):
        super().__init__()
        self.keywords: List[str] = []
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 说明文本
        info_label = QLabel(
            "配置邮件内容匹配的关键字，当邮件主题或内容包含这些关键字时，将自动推送到钉钉。\n"
            "支持多个关键字，任意一个匹配即可触发推送。"
        )
        info_label.setStyleSheet("color: #666; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 关键字列表
        self._create_keyword_list(layout)
        
        # 添加关键字区域
        self._create_add_keyword_area(layout)
        
        # 匹配选项
        self._create_match_options(layout)
    
    def _create_keyword_list(self, parent_layout):
        """创建关键字列表"""
        list_group = QGroupBox("已配置的关键字")
        list_layout = QVBoxLayout(list_group)
        
        # 关键字列表
        self.keyword_list = QListWidget()
        self.keyword_list.setMinimumHeight(200)
        list_layout.addWidget(self.keyword_list)
        
        # 按钮组
        button_layout = QHBoxLayout()
        list_layout.addLayout(button_layout)
        
        self.remove_button = QPushButton("➖ 删除选中")
        self.clear_button = QPushButton("🗑️ 清空全部")
        
        self.remove_button.setEnabled(False)
        
        button_layout.addWidget(self.remove_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()
        
        parent_layout.addWidget(list_group)
    
    def _create_add_keyword_area(self, parent_layout):
        """创建添加关键字区域"""
        add_group = QGroupBox("添加新关键字")
        add_layout = QFormLayout(add_group)
        
        # 关键字输入
        self.keyword_edit = QLineEdit()
        self.keyword_edit.setPlaceholderText("输入关键字，如：告警通知")
        add_layout.addRow("关键字:", self.keyword_edit)
        
        # 批量添加
        self.batch_edit = QTextEdit()
        self.batch_edit.setMaximumHeight(80)
        self.batch_edit.setPlaceholderText("批量添加关键字，每行一个")
        add_layout.addRow("批量添加:", self.batch_edit)
        
        # 按钮组
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("➕ 添加")
        self.batch_add_button = QPushButton("📝 批量添加")
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.batch_add_button)
        button_layout.addStretch()
        
        add_layout.addRow("", button_layout)
        
        parent_layout.addWidget(add_group)
    
    def _create_match_options(self, parent_layout):
        """创建匹配选项"""
        options_group = QGroupBox("匹配选项")
        options_layout = QVBoxLayout(options_group)
        
        # 大小写敏感
        self.case_sensitive_check = QCheckBox("大小写敏感")
        self.case_sensitive_check.setToolTip("勾选后，关键字匹配将区分大小写")
        options_layout.addWidget(self.case_sensitive_check)
        
        # 全词匹配
        self.whole_word_check = QCheckBox("全词匹配")
        self.whole_word_check.setToolTip("勾选后，只有完整的词语匹配才会触发")
        options_layout.addWidget(self.whole_word_check)
        
        # 正则表达式
        self.regex_check = QCheckBox("启用正则表达式")
        self.regex_check.setToolTip("勾选后，关键字将作为正则表达式进行匹配")
        options_layout.addWidget(self.regex_check)
        
        parent_layout.addWidget(options_group)
    
    def _connect_signals(self):
        """连接信号"""
        self.keyword_list.currentRowChanged.connect(self.on_keyword_selected)
        self.add_button.clicked.connect(self.add_keyword)
        self.batch_add_button.clicked.connect(self.batch_add_keywords)
        self.remove_button.clicked.connect(self.remove_keyword)
        self.clear_button.clicked.connect(self.clear_keywords)
        
        # 回车键添加关键字
        self.keyword_edit.returnPressed.connect(self.add_keyword)
    
    def load_config(self):
        """加载配置"""
        self.keywords = config_manager.get_keywords().copy()
        self._update_keyword_list()
        
        # 加载匹配选项（如果配置中有的话）
        # 这里可以扩展配置管理器来支持这些选项
    
    def save_config(self):
        """保存配置"""
        config_manager.set_keywords(self.keywords)
        
        # 保存匹配选项（如果需要的话）
        # 这里可以扩展配置管理器来保存这些选项
    
    def validate(self) -> bool:
        """验证配置"""
        if not self.keywords:
            QMessageBox.warning(self, "验证失败", "至少需要配置一个关键字")
            return False
        
        # 如果启用了正则表达式，验证正则表达式的有效性
        if self.regex_check.isChecked():
            import re
            for keyword in self.keywords:
                try:
                    re.compile(keyword)
                except re.error as e:
                    QMessageBox.warning(self, "验证失败", f"无效的正则表达式: {keyword}\n错误: {str(e)}")
                    return False
        
        return True
    
    def _update_keyword_list(self):
        """更新关键字列表"""
        self.keyword_list.clear()
        
        for keyword in self.keywords:
            item = QListWidgetItem(keyword)
            self.keyword_list.addItem(item)
        
        # 更新按钮状态
        self.clear_button.setEnabled(len(self.keywords) > 0)
    
    def on_keyword_selected(self, row):
        """关键字被选中"""
        has_selection = row >= 0
        self.remove_button.setEnabled(has_selection)
    
    def add_keyword(self):
        """添加关键字"""
        keyword = self.keyword_edit.text().strip()
        
        if not keyword:
            QMessageBox.warning(self, "输入错误", "请输入关键字")
            return
        
        if keyword in self.keywords:
            QMessageBox.warning(self, "重复添加", "该关键字已存在")
            return
        
        # 如果启用了正则表达式，验证正则表达式
        if self.regex_check.isChecked():
            import re
            try:
                re.compile(keyword)
            except re.error as e:
                QMessageBox.warning(self, "正则表达式错误", f"无效的正则表达式: {str(e)}")
                return
        
        self.keywords.append(keyword)
        self._update_keyword_list()
        self.keyword_edit.clear()
        
        QMessageBox.information(self, "成功", "关键字添加成功")
    
    def batch_add_keywords(self):
        """批量添加关键字"""
        text = self.batch_edit.toPlainText().strip()
        
        if not text:
            QMessageBox.warning(self, "输入错误", "请输入要批量添加的关键字")
            return
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        if not lines:
            QMessageBox.warning(self, "输入错误", "没有有效的关键字")
            return
        
        added_count = 0
        duplicate_count = 0
        error_keywords = []
        
        for keyword in lines:
            if keyword in self.keywords:
                duplicate_count += 1
                continue
            
            # 如果启用了正则表达式，验证正则表达式
            if self.regex_check.isChecked():
                import re
                try:
                    re.compile(keyword)
                except re.error:
                    error_keywords.append(keyword)
                    continue
            
            self.keywords.append(keyword)
            added_count += 1
        
        self._update_keyword_list()
        self.batch_edit.clear()
        
        # 显示结果
        result_msg = f"批量添加完成\n成功添加: {added_count} 个"
        if duplicate_count > 0:
            result_msg += f"\n重复跳过: {duplicate_count} 个"
        if error_keywords:
            result_msg += f"\n正则表达式错误: {len(error_keywords)} 个"
        
        QMessageBox.information(self, "批量添加结果", result_msg)
    
    def remove_keyword(self):
        """删除关键字"""
        current_row = self.keyword_list.currentRow()
        if current_row < 0:
            return
        
        keyword = self.keywords[current_row]
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除关键字 '{keyword}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            del self.keywords[current_row]
            self._update_keyword_list()
            QMessageBox.information(self, "成功", "关键字删除成功")
    
    def clear_keywords(self):
        """清空所有关键字"""
        if not self.keywords:
            return
        
        reply = QMessageBox.question(
            self, "确认清空", 
            f"确定要清空所有 {len(self.keywords)} 个关键字吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.keywords.clear()
            self._update_keyword_list()
            QMessageBox.information(self, "成功", "所有关键字已清空")
    
    def reset_config(self):
        """重置配置"""
        self.keywords = ["告警通知"]  # 恢复默认关键字
        self._update_keyword_list()
        self.keyword_edit.clear()
        self.batch_edit.clear()
        self.case_sensitive_check.setChecked(False)
        self.whole_word_check.setChecked(False)
        self.regex_check.setChecked(False)
