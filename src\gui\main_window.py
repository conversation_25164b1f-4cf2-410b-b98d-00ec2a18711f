# -*- coding: utf-8 -*-
"""
主窗口
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                               QSplitter, QToolBar, QStatusBar, QPushButton, 
                               QLabel, QMessageBox, QProgressBar)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QAction, QIcon
from src.gui.email_list_widget import EmailListWidget
from src.gui.email_detail_widget import EmailDetailWidget
from src.gui.config_dialog import ConfigDialog
# 使用简化后的邮件告警推送器
from src.core.universal_email_pusher import EmailAlertPusher
from src.email_handler.simple_email_client import SimpleEmailClient
from src.pusher.dingtalk_pusher import DingTalkPusher
from src.config.config_manager import config_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()

        # 简洁的邮件告警系统
        self.email_alert_pusher = None
        self.config_dialog = None

        # 简单的定时器系统 - Linus式设计
        self.auto_refresh_timer = QTimer()
        self.countdown_timer = QTimer()
        self.countdown_seconds = 0

        self._init_ui()
        self._init_email_service()
        self._connect_signals()
        self._load_settings()

        # 延迟启动邮件服务
        QTimer.singleShot(1000, self._start_email_service)
    
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("邮件告警推送程序")
        self.setMinimumSize(1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建工具栏
        self._create_toolbar()
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建邮件列表
        self.email_list_widget = EmailListWidget()
        splitter.addWidget(self.email_list_widget)
        
        # 创建邮件详情
        self.email_detail_widget = EmailDetailWidget()
        splitter.addWidget(self.email_detail_widget)
        
        # 设置分割器比例
        splitter.setSizes([600, 400])
        
        # 创建状态栏
        self._create_statusbar()
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 刷新按钮
        refresh_action = QAction("🔄 刷新", self)
        refresh_action.setToolTip("立即刷新邮件")
        refresh_action.triggered.connect(self.refresh_emails)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # 配置按钮
        config_action = QAction("⚙️ 设置", self)
        config_action.setToolTip("打开设置")
        config_action.triggered.connect(self.open_config)
        toolbar.addAction(config_action)
        
        toolbar.addSeparator()
        
        # 邮箱状态显示
        self.email_status_label = QLabel("邮箱: 未配置")
        self.email_status_label.setStyleSheet("color: #666; font-weight: bold;")
        toolbar.addWidget(self.email_status_label)

        toolbar.addSeparator()

        # 自动刷新状态
        self.auto_refresh_label = QLabel("自动刷新: 关闭")
        toolbar.addWidget(self.auto_refresh_label)

        # 添加弹性空间
        toolbar.addWidget(QWidget())
        
        # 推送按钮
        self.push_button = QPushButton("📤 推送选中邮件")
        self.push_button.setEnabled(False)
        self.push_button.clicked.connect(self.push_selected_email)
        toolbar.addWidget(self.push_button)
    
    def _create_statusbar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 连接状态
        self.connection_label = QLabel("🔴 未连接")
        self.connection_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
        self.status_bar.addPermanentWidget(self.connection_label)

        # 分隔符
        separator1 = QLabel(" | ")
        separator1.setStyleSheet("color: #999;")
        self.status_bar.addPermanentWidget(separator1)

        # Webhook状态
        self.webhook_label = QLabel("📡 Webhook: 0")
        self.webhook_label.setStyleSheet("color: #666;")
        self.status_bar.addPermanentWidget(self.webhook_label)

        # 分隔符
        separator2 = QLabel(" | ")
        separator2.setStyleSheet("color: #999;")
        self.status_bar.addPermanentWidget(separator2)

        # 统计信息
        self.stats_label = QLabel("邮件: 0")
        self.stats_label.setStyleSheet("color: #666;")
        self.status_bar.addPermanentWidget(self.stats_label)

    def _init_email_service(self):
        """初始化邮件服务 - Linus式简洁设计"""
        try:
            # 获取配置
            email_configs = config_manager.get_email_accounts()
            webhooks = config_manager.get_webhooks()
            keywords = config_manager.get_keywords()

            if not email_configs:
                logger.info("未配置邮箱，邮件服务暂不启动")
                return

            # 创建邮件客户端
            email_clients = []
            for config in email_configs:
                try:
                    client = SimpleEmailClient(config)
                    email_clients.append(client)
                    logger.info(f"邮件客户端创建成功: {config.get('email', 'unknown')}")
                except Exception as e:
                    logger.error(f"邮件客户端创建失败: {e}")

            if not email_clients:
                logger.warning("没有可用的邮件客户端")
                return

            # 创建推送器
            pusher = None
            if webhooks:
                pusher = DingTalkPusher(webhooks)
                logger.info(f"推送器创建成功，Webhook数量: {len(webhooks)}")
            else:
                logger.warning("未配置Webhook，推送功能不可用")

            # 创建邮件告警推送器 - 核心组件
            self.email_alert_pusher = EmailAlertPusher(
                email_clients=email_clients,
                pusher=pusher,
                keywords=keywords
            )

            logger.info("✅ 邮件告警服务初始化完成")

        except Exception as e:
            logger.error(f"❌ 邮件服务初始化失败: {e}")
            self.update_status(f"邮件服务初始化失败: {str(e)}")

    def _connect_signals(self):
        """连接信号"""
        # 新的简洁信号连接 - 不需要复杂的邮件服务信号
        
        # 邮件列表信号
        self.email_list_widget.email_selected.connect(self.email_detail_widget.show_email)
        self.email_list_widget.email_selected.connect(self.on_email_selected)
        self.email_list_widget.push_requested.connect(self.push_email)

        # 邮件详情信号
        self.email_detail_widget.push_requested.connect(self.push_email)

        # 简单定时器信号 - Linus式设计
        self.auto_refresh_timer.timeout.connect(self.check_emails)
        self.countdown_timer.timeout.connect(self.update_countdown)

    def _start_email_service(self):
        """启动邮件服务"""
        if not self.email_alert_pusher:
            self.update_status("邮件服务未初始化")
            return

        try:
            # 初始化：标记所有现有邮件为已见
            self.email_alert_pusher.initialize()

            # 刷新邮件列表显示
            self.refresh_email_list()

            # 启动定时检查
            self.start_auto_refresh()

            self.update_status("邮件告警服务已启动")
            logger.info("🚀 邮件告警服务启动完成")

        except Exception as e:
            logger.error(f"❌ 启动邮件服务失败: {e}")
            self.update_status(f"启动失败: {str(e)}")

    def _load_settings(self):
        """加载设置"""
        ui_settings = config_manager.get_ui_settings()
        
        # 设置窗口大小和位置
        if "window_size" in ui_settings:
            self.resize(*ui_settings["window_size"])
        if "window_position" in ui_settings:
            self.move(*ui_settings["window_position"])
        
        # 更新状态显示
        self.update_email_status()
        self.update_webhook_status()
    
    def _save_settings(self):
        """保存设置"""
        ui_settings = {
            "window_size": [self.width(), self.height()],
            "window_position": [self.x(), self.y()]
        }
        config_manager.set_ui_settings(ui_settings)
    
    def refresh_emails(self):
        """手动刷新邮件 - 重置定时器"""
        logger.info("🔄 用户手动刷新邮件")

        # 执行检查
        self.check_emails()

        # 重置倒计时（如果自动刷新开启）
        auto_refresh_config = config_manager.get_auto_refresh_config()
        if auto_refresh_config["enabled"]:
            self.reset_countdown()
            logger.info("⏰ 倒计时已重置")

    def check_emails(self):
        """检查新邮件 - 核心功能"""
        if not self.email_alert_pusher:
            self.update_status("邮件服务未初始化")
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.update_status("正在检查新邮件...")

        try:
            # 调用核心逻辑：检查并推送新邮件
            success = self.email_alert_pusher.check_and_push_new_emails()

            if success:
                # 更新状态显示
                status = self.email_alert_pusher.get_status()
                self.update_status(f"检查完成，已见邮件: {status['seen_emails']}封")
                self.stats_label.setText(f"邮件: {status['seen_emails']}")

                # 更新邮件列表显示
                self.refresh_email_list()
            else:
                self.update_status("检查邮件失败")

        except Exception as e:
            logger.error(f"检查邮件异常: {e}")
            self.update_status(f"检查异常: {str(e)}")

        finally:
            self.progress_bar.setVisible(False)

    def refresh_email_list(self):
        """刷新邮件列表显示 - 异步版本，避免阻塞UI"""
        if not self.email_alert_pusher:
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.update_status("正在刷新邮件列表...")

        # 使用QTimer延迟执行，避免阻塞UI
        QTimer.singleShot(10, self._do_refresh_email_list)

    def _do_refresh_email_list(self):
        """实际执行邮件列表刷新 - 使用统一的邮件获取方法"""
        try:
            # 使用推送器的统一方法获取邮件（避免重复代码）
            all_emails = self.email_alert_pusher.fetch_all_emails_fast()

            # 按时间排序（最新的在前）
            all_emails.sort(key=lambda x: x.date, reverse=True)

            # 更新邮件列表显示
            self.email_list_widget.update_emails(all_emails)

            # 更新状态
            self.update_status(f"邮件列表已刷新，共 {len(all_emails)} 封邮件")
            logger.info(f"📧 邮件列表已更新，共 {len(all_emails)} 封邮件")

        except Exception as e:
            logger.error(f"刷新邮件列表失败: {e}")
            self.update_status(f"刷新失败: {str(e)}")
        finally:
            # 隐藏进度条
            self.progress_bar.setVisible(False)

    def open_config(self):
        """打开配置对话框"""
        if not self.config_dialog:
            self.config_dialog = ConfigDialog(self)
            self.config_dialog.config_saved.connect(self.on_config_saved)
        
        self.config_dialog.show()
        self.config_dialog.raise_()
        self.config_dialog.activateWindow()
    
    def push_selected_email(self):
        """推送选中的邮件"""
        selected_email = self.email_list_widget.get_selected_email()
        if selected_email:
            self.push_email(selected_email)
    
    def push_email(self, email_msg):
        """推送邮件"""
        if not self.email_alert_pusher or not self.email_alert_pusher.pusher:
            self.update_status("推送器未初始化")
            return

        self.update_status("正在推送邮件...")

        try:
            # 直接调用推送器推送邮件
            self.email_alert_pusher.push_email(email_msg)
            self.update_status(f"邮件推送完成: {email_msg.subject}")
            logger.info(f"手动推送邮件: {email_msg.subject}")

        except Exception as e:
            logger.error(f"推送邮件失败: {e}")
            self.update_status(f"推送失败: {str(e)}")
    
    def on_emails_updated(self, emails):
        """邮件列表更新"""
        total_emails = len(emails)
        new_emails = sum(1 for email in emails if email.is_new)
        self.update_stats_display(total_emails, new_emails)

    def on_connection_status_changed(self, email, success, message):
        """连接状态变化"""
        if success:
            self.connection_label.setText("🟢 已连接")
            self.connection_label.setStyleSheet("color: #2e7d32; font-weight: bold;")
            self.connection_label.setToolTip(f"邮箱 {email} 连接成功")
        else:
            self.connection_label.setText("🔴 连接失败")
            self.connection_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
            self.connection_label.setToolTip(f"邮箱 {email} 连接失败: {message}")

        # 同时更新邮箱状态显示
        self.update_email_status_with_connection(success)

    def on_new_email_received(self, email_msg):
        """收到新邮件"""
        self.update_status(f"收到新邮件: {email_msg.subject}")

        # 可以在这里添加桌面通知等功能
        # self.show_notification(f"新邮件: {email_msg.subject}")
    
    def on_push_completed(self, success, message):
        """推送完成"""
        self.progress_bar.setVisible(False)

        if success:
            self.update_status(f"推送成功: {message}")
        else:
            self.update_status(f"推送失败: {message}")
            # 移除弹窗，只在状态栏显示错误信息
            logger.warning(f"推送失败: {message}")
    
    def on_email_selected(self, email_msg):
        """邮件被选中"""
        self.push_button.setEnabled(email_msg is not None)
    
    def on_config_saved(self):
        """配置保存后"""
        # 重新初始化邮件服务
        self.stop_auto_refresh()
        self._init_email_service()

        # 延迟重启服务
        QTimer.singleShot(1000, self._start_email_service)

        self.update_email_status()
        self.update_webhook_status()
        self.update_status("配置已更新，正在重启邮件服务...")
    
    def update_status(self, message):
        """更新状态"""
        self.status_label.setText(message)
        
        # 3秒后恢复默认状态
        QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
        
        # 隐藏进度条
        if self.progress_bar.isVisible():
            QTimer.singleShot(500, lambda: self.progress_bar.setVisible(False))
    


    def update_countdown(self):
        """更新倒计时 - 简洁实现"""
        if self.countdown_seconds > 0:
            self.countdown_seconds -= 1

            # 获取配置
            auto_refresh_config = config_manager.get_auto_refresh_config()
            if auto_refresh_config["enabled"]:
                interval = auto_refresh_config['interval']
                self.auto_refresh_label.setText(f"自动刷新: {interval}秒 (倒计时: {self.countdown_seconds})")

                # 当倒计时到0时，重置倒计时
                if self.countdown_seconds == 0:
                    self.countdown_seconds = interval

    def reset_countdown(self):
        """重置倒计时 - 简洁实现"""
        auto_refresh_config = config_manager.get_auto_refresh_config()
        if auto_refresh_config["enabled"]:
            self.countdown_seconds = auto_refresh_config['interval']
            interval = auto_refresh_config['interval']
            self.auto_refresh_label.setText(f"自动刷新: {interval}秒 (倒计时: {self.countdown_seconds})")

    def update_email_status(self):
        """更新邮箱状态"""
        email_accounts = config_manager.get_email_accounts()

        if not email_accounts:
            self.email_status_label.setText("邮箱: 未配置")
            self.email_status_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
            self.connection_label.setText("🔴 未连接")
            self.connection_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
        else:
            enabled_accounts = [acc for acc in email_accounts if acc.get("enabled", True)]
            if enabled_accounts:
                if len(enabled_accounts) == 1:
                    email_addr = enabled_accounts[0].get("email", "未知")
                    self.email_status_label.setText(f"邮箱: {email_addr}")
                else:
                    self.email_status_label.setText(f"邮箱: {len(enabled_accounts)}个账户")

                self.email_status_label.setStyleSheet("color: #2e7d32; font-weight: bold;")
                self.connection_label.setText("🟢 已配置")
                self.connection_label.setStyleSheet("color: #2e7d32; font-weight: bold;")
            else:
                self.email_status_label.setText("邮箱: 已禁用")
                self.email_status_label.setStyleSheet("color: #f57c00; font-weight: bold;")
                self.connection_label.setText("🟡 已禁用")
                self.connection_label.setStyleSheet("color: #f57c00; font-weight: bold;")

    def update_email_status_with_connection(self, is_connected):
        """根据连接状态更新邮箱状态显示"""
        email_accounts = config_manager.get_email_accounts()

        if not email_accounts:
            self.email_status_label.setText("邮箱: 未配置")
            self.email_status_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
        else:
            enabled_accounts = [acc for acc in email_accounts if acc.get("enabled", True)]
            if enabled_accounts:
                if len(enabled_accounts) == 1:
                    email_addr = enabled_accounts[0].get("email", "未知")
                    if is_connected:
                        self.email_status_label.setText(f"邮箱: {email_addr} ✓")
                        self.email_status_label.setStyleSheet("color: #2e7d32; font-weight: bold;")
                    else:
                        self.email_status_label.setText(f"邮箱: {email_addr} ✗")
                        self.email_status_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
                else:
                    if is_connected:
                        self.email_status_label.setText(f"邮箱: {len(enabled_accounts)}个账户 ✓")
                        self.email_status_label.setStyleSheet("color: #2e7d32; font-weight: bold;")
                    else:
                        self.email_status_label.setText(f"邮箱: {len(enabled_accounts)}个账户 ✗")
                        self.email_status_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
            else:
                self.email_status_label.setText("邮箱: 已禁用")
                self.email_status_label.setStyleSheet("color: #f57c00; font-weight: bold;")

    def update_webhook_status(self):
        """更新Webhook状态"""
        webhooks = config_manager.get_webhooks()
        webhook_count = len(webhooks)

        if webhook_count == 0:
            self.webhook_label.setText("📡 Webhook: 未配置")
            self.webhook_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
        else:
            self.webhook_label.setText(f"📡 Webhook: {webhook_count}个")
            self.webhook_label.setStyleSheet("color: #2e7d32; font-weight: bold;")

    def update_stats_display(self, total_emails=0, new_emails=0):
        """更新统计信息显示"""
        if new_emails > 0:
            self.stats_label.setText(f"邮件: {total_emails} (新: {new_emails})")
            self.stats_label.setStyleSheet("color: #1976d2; font-weight: bold;")
        else:
            self.stats_label.setText(f"邮件: {total_emails}")
            self.stats_label.setStyleSheet("color: #666;")
    
    def start_auto_refresh(self):
        """启动自动刷新 - Linus式简洁实现"""
        auto_refresh_config = config_manager.get_auto_refresh_config()

        if auto_refresh_config["enabled"]:
            interval = auto_refresh_config['interval']

            # 启动主定时器
            self.auto_refresh_timer.start(interval * 1000)  # 转换为毫秒

            # 启动倒计时显示
            self.countdown_seconds = interval
            self.countdown_timer.start(1000)  # 每秒更新

            # 更新显示
            self.auto_refresh_label.setText(f"自动刷新: {interval}秒 (倒计时: {interval})")

            logger.info(f"✅ 自动刷新已启动，间隔: {interval}秒")
        else:
            self.auto_refresh_label.setText("自动刷新: 关闭")
            logger.info("自动刷新已禁用")

    def stop_auto_refresh(self):
        """停止自动刷新 - 简洁实现"""
        # 停止所有定时器
        if self.auto_refresh_timer.isActive():
            self.auto_refresh_timer.stop()
        if self.countdown_timer.isActive():
            self.countdown_timer.stop()

        # 更新显示
        self.auto_refresh_label.setText("自动刷新: 关闭")
        logger.info("🛑 自动刷新已停止")

    def closeEvent(self, event):
        """关闭事件"""
        self._save_settings()

        # 停止定时器
        self.stop_auto_refresh()

        event.accept()
        logger.info("应用程序已关闭")
